# 麻将助手代理模式使用指南

## 概述

麻将助手代理模式是一个HTTP/HTTPS隧道代理服务器，用于拦截雀魂客户端的网络流量，提取游戏数据并发送给麻将助手进行分析。

## 架构图

```
雀魂客户端 ←→ Proxifier ←→ 代理服务器 ←→ 雀魂服务器
                              ↓
                          麻将助手
```

## 安装和配置

### 1. 编译程序

```bash
go build -o mahjong-helper.exe
```

### 2. 启动代理模式

```bash
# 使用默认配置启动代理
./mahjong-helper.exe -proxy

# 指定代理端口
./mahjong-helper.exe -proxy -proxy-port 9999

# 同时启动助手服务器和代理
./mahjong-helper.exe -proxy -port 12121
```

### 3. 配置文件

首次运行时会自动生成 `proxy_config.json` 配置文件：

```json
{
  "proxy": {
    "host": "127.0.0.1",
    "port": 8888,
    "max_connections": 100,
    "connection_timeout": 10
  },
  "mahjong_helper": {
    "url": "http://localhost:12121/majsoul",
    "timeout": 1.0,
    "retry_count": 3,
    "retry_delay": 0.5
  },
  "logging": {
    "level": "INFO",
    "file": "proxy.log",
    "max_size": "10MB",
    "backup_count": 5
  },
  "majsoul": {
    "hosts": [
      "majsoul.com",
      "maj-soul.com", 
      "mahjongsoul.game.yo-star.com",
      "game.mahjongsoul.com"
    ],
    "ports": [443, 4130, 4131, 4132, 4133, 4134, 4135],
    "data_filter": {
      "min_packet_size": 10,
      "max_packet_size": 65536,
      "enable_websocket_parsing": true,
      "enable_json_parsing": true,
      "enable_protobuf_parsing": false
    }
  },
  "advanced": {
    "buffer_size": 8192,
    "keep_alive": true,
    "tcp_nodelay": true
  }
}
```

## Proxifier 配置

### 1. 安装 Proxifier

从官网下载并安装 Proxifier：https://www.proxifier.com/

### 2. 配置代理服务器

1. 打开 Proxifier
2. 菜单：Profile → Proxy Servers
3. 点击 "Add" 添加新代理
4. 配置如下：
   - Address: 127.0.0.1
   - Port: 8888 (或你设置的端口)
   - Protocol: HTTPS

### 3. 配置代理规则

1. 菜单：Profile → Proxification Rules
2. 点击 "Add" 添加新规则
3. 配置如下：
   - Name: Majsoul
   - Applications: 选择雀魂客户端程序
   - Target hosts: majsoul.com; *.majsoul.com; mahjongsoul.game.yo-star.com; *.mahjongsoul.game.yo-star.com
   - Action: Proxy HTTPS 127.0.0.1

### 4. 启用代理

确保 Proxifier 处于启用状态，然后启动雀魂客户端。

## 使用流程

### 1. 启动服务

```bash
# 终端1：启动麻将助手服务器
./mahjong-helper.exe -majsoul

# 终端2：启动代理服务器
./mahjong-helper.exe -proxy
```

### 2. 配置 Proxifier

按照上述配置步骤设置 Proxifier。

### 3. 启动雀魂

启动雀魂客户端，代理服务器会自动拦截流量并发送给助手。

### 4. 查看日志

- 代理日志：`proxy.log`
- 助手日志：`log/gamedata-*.log`

## 故障排除

### 1. 代理服务器无法启动

**错误**: `端口已被占用`

**解决**: 
- 检查端口是否被其他程序占用
- 使用 `-proxy-port` 参数指定其他端口

### 2. 无法连接到助手

**错误**: `助手连接失败`

**解决**:
- 确保麻将助手服务器已启动
- 检查配置文件中的助手URL是否正确
- 检查防火墙设置

### 3. 雀魂连接失败

**错误**: `连接目标服务器失败`

**解决**:
- 检查网络连接
- 确认雀魂服务器地址正确
- 检查DNS设置

### 4. 数据未被拦截

**可能原因**:
- Proxifier 规则配置错误
- 雀魂客户端未通过代理
- 数据过滤器设置过于严格

**解决**:
- 检查 Proxifier 日志
- 调整数据过滤器配置
- 启用调试日志查看详细信息

## 高级配置

### 1. 调试模式

```bash
# 启用详细日志
./mahjong-helper.exe -proxy
# 然后修改配置文件中的 logging.level 为 "DEBUG"
```

### 2. 性能优化

- 调整 `buffer_size` 提高数据传输效率
- 调整 `max_connections` 限制并发连接数
- 启用 `tcp_nodelay` 减少延迟

### 3. 数据过滤

- `min_packet_size`: 最小数据包大小，过滤掉太小的包
- `max_packet_size`: 最大数据包大小，避免处理过大的包
- `enable_websocket_parsing`: 启用WebSocket解析
- `enable_json_parsing`: 启用JSON数据识别
- `enable_protobuf_parsing`: 启用Protobuf数据识别

## 命令行参数

```
-proxy              启用代理模式
-proxy-port <port>  指定代理端口 (默认: 8888)
-port <port>        指定助手服务端口 (默认: 12121)
-h                  显示帮助信息
```

## 日志说明

### 代理日志格式

```
[时间] [级别] 消息内容
```

### 常见日志消息

- `代理服务器已启动`: 代理成功启动
- `检测到雀魂连接`: 成功识别雀魂流量
- `助手连接失败`: 无法连接到助手服务器
- `连接目标服务器失败`: 无法连接到目标服务器

## 安全注意事项

1. 代理服务器仅监听本地地址，不接受外部连接
2. 所有拦截的数据仅用于游戏分析，不会存储或传输到其他地方
3. 建议在防火墙中限制代理服务器的网络访问权限

## 技术支持

如遇问题，请提供以下信息：

1. 错误日志内容
2. 配置文件内容
3. 操作系统版本
4. 雀魂客户端版本
5. Proxifier 版本和配置

提交问题到：https://github.com/EndlessCheng/mahjong-helper/issues
