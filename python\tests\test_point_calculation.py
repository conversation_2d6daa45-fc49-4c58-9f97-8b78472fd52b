"""
点数计算测试 - 对照Go版本的point_test.go
"""
import unittest
from mahjong_py import str_to_tiles34, calc_point_ron, calc_point_tsumo_sum, calc_point_hand
from mahjong_py.config import set_consider_old_yaku


class TestPointCalculation(unittest.TestCase):
    """点数计算测试类"""
    
    def setUp(self):
        """测试前设置"""
        set_consider_old_yaku(False)  # 默认关闭老役
    
    def test_calc_point_ron_basic(self):
        """测试基础荣和点数计算 - 对照Go版本TestCalcPointRon"""
        # 基础点数测试
        self.assertEqual(3600, calc_point_ron(1, 110, 0, False))  # saki
        self.assertEqual(5200, calc_point_ron(3, 40, 0, False))
        self.assertEqual(6400, calc_point_ron(3, 50, 0, False))
        self.assertEqual(7700, calc_point_ron(3, 60, 0, False))
        self.assertEqual(8000, calc_point_ron(3, 70, 0, False))
        
        # 亲家点数测试
        self.assertEqual(7700, calc_point_ron(3, 40, 0, True))
        self.assertEqual(11600, calc_point_ron(4, 30, 0, True))
        self.assertEqual(12000, calc_point_ron(4, 40, 0, True))
        
        # 役满点数测试 - 子家
        self.assertEqual(32000, calc_point_ron(0, 0, 1, False))
        self.assertEqual(64000, calc_point_ron(0, 0, 2, False))
        self.assertEqual(96000, calc_point_ron(0, 0, 3, False))
        self.assertEqual(128000, calc_point_ron(0, 0, 4, False))
        self.assertEqual(160000, calc_point_ron(0, 0, 5, False))
        self.assertEqual(192000, calc_point_ron(0, 0, 6, False))
        
        # 役满点数测试 - 亲家
        self.assertEqual(48000, calc_point_ron(0, 0, 1, True))
        self.assertEqual(96000, calc_point_ron(0, 0, 2, True))
        self.assertEqual(144000, calc_point_ron(0, 0, 3, True))
        self.assertEqual(192000, calc_point_ron(0, 0, 4, True))
        self.assertEqual(240000, calc_point_ron(0, 0, 5, True))
        self.assertEqual(288000, calc_point_ron(0, 0, 6, True))
    
    def test_calc_point_tsumo_sum_basic(self):
        """测试基础自摸点数计算 - 对照Go版本TestCalcPointTsumoSum"""
        # 基础点数测试
        self.assertEqual(3600, calc_point_tsumo_sum(1, 110, 0, False))  # saki
        self.assertEqual(5200, calc_point_tsumo_sum(3, 40, 0, False))
        self.assertEqual(6400, calc_point_tsumo_sum(3, 50, 0, False))
        self.assertEqual(7900, calc_point_tsumo_sum(3, 60, 0, False))
        self.assertEqual(8000, calc_point_tsumo_sum(3, 70, 0, False))
        
        # 亲家点数测试
        self.assertEqual(7800, calc_point_tsumo_sum(3, 40, 0, True))
        self.assertEqual(12000, calc_point_tsumo_sum(4, 40, 0, True))
    
    def test_calc_point_with_hands(self):
        """测试具体手牌的点数计算 - 对照Go版本TestCalcRonPointWithHands"""
        test_cases = [
            # (手牌, 和牌, 期望点数, 描述)
            ("11m 112233445566z", "1m", 12000, "七对 混老头 混一色"),
            ("345m 345s 334455p 44z", "3m", 7700, "平和 一杯口 三色"),
            ("333m 333s 333345p 11z", "3m", 2600, "三色同刻"),
            ("22334455m 234s 234p", "3m", 8000, "高点法取[一杯口 三色 断幺]"),
            ("234m 333p 55666777z", "3m", 12000, "三暗刻 役牌 役牌 小三元"),
            ("123445566789m 11z", "3m", 12000, "一杯口 一气 混一色"),
            ("123m 123999s 11155z", "3m", 3200, "混全"),
            ("334455m 667788s 77z", "3m", 5200, "两杯口"),
            ("334455m 667788s 44z", "3m", 7700, "平和 两杯口"),
            ("123m 123999s 11789p", "3m", 5200, "纯全"),
            ("345m 12355789s 222z", "3m", 2600, "役牌 役牌"),
        ]
        
        for hand_str, win_tile_str, expected_point, description in test_cases:
            with self.subTest(hand=hand_str, win=win_tile_str, desc=description):
                tiles34, _ = str_to_tiles34(hand_str)
                win_tile34, _ = str_to_tiles34(win_tile_str)
                win_tile = next(i for i, c in enumerate(win_tile34) if c > 0)
                
                point = calc_point_hand(
                    tiles34, win_tile, 
                    is_tsumo=False, is_parent=False,
                    round_wind=28, self_wind=28  # 南风场，自风南
                )
                self.assertEqual(expected_point, point, f"Failed for {description}")
    
    def test_yakuman_hands(self):
        """测试役满手牌的点数计算"""
        yakuman_cases = [
            ("11122345678999m", "3m", 32000, "九莲宝灯"),
            ("11122345678999m", "2m", 64000, "四暗刻"),
            ("11122233344455z", "5z", 160000, "字一色+大四喜+大三元"),
        ]
        
        for hand_str, win_tile_str, expected_point, description in yakuman_cases:
            with self.subTest(hand=hand_str, win=win_tile_str, desc=description):
                tiles34, _ = str_to_tiles34(hand_str)
                win_tile34, _ = str_to_tiles34(win_tile_str)
                win_tile = next(i for i, c in enumerate(win_tile34) if c > 0)
                
                point = calc_point_hand(
                    tiles34, win_tile,
                    is_tsumo=False, is_parent=False,
                    round_wind=28, self_wind=28
                )
                self.assertEqual(expected_point, point, f"Failed for {description}")
    
    def test_riichi_points(self):
        """测试立直时的点数计算"""
        # 基础立直
        tiles34, _ = str_to_tiles34("345m 222789p 333s 66z")
        win_tile34, _ = str_to_tiles34("3m")
        win_tile = next(i for i, c in enumerate(win_tile34) if c > 0)
        
        point = calc_point_hand(
            tiles34, win_tile,
            is_tsumo=False, is_parent=False,
            round_wind=28, self_wind=30,  # 南风场，自风西
            is_riichi=True
        )
        self.assertEqual(1300, point, "立直基础点数")
    
    def test_dora_points(self):
        """测试宝牌时的点数计算"""
        # 测试不同宝牌数量的点数变化
        expected_points = [1300, 2600, 5200, 8000, 8000, 12000, 12000, 16000, 16000, 16000, 24000, 24000, 32000]
        
        tiles34, _ = str_to_tiles34("345m 222789p 333s 66z")
        win_tile34, _ = str_to_tiles34("3m")
        win_tile = next(i for i, c in enumerate(win_tile34) if c > 0)
        
        for dora_count in range(13):
            with self.subTest(dora_count=dora_count):
                # 模拟宝牌数量（这里简化处理，实际应该通过dora_indicators参数）
                point = calc_point_hand(
                    tiles34, win_tile,
                    is_tsumo=False, is_parent=False,
                    round_wind=28, self_wind=30,
                    is_riichi=True,
                    # 这里需要根据实际API调整宝牌参数
                )
                # 注意：由于Python版本的宝牌处理可能与Go版本不同，这个测试可能需要调整
                if dora_count == 0:  # 只测试无宝牌的情况
                    self.assertEqual(expected_points[dora_count], point)


if __name__ == '__main__':
    unittest.main()
