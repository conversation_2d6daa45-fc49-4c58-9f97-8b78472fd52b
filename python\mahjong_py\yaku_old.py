from __future__ import annotations
from typing import List, Tuple
from .yaku import HandInfo
from .yaku_data import *


def _check_all_pairs(tiles: List[int]) -> bool:
    """检查是否所有牌都是对子（用于古役满）"""
    for c in tiles:
        if c != 2:
            return False
    return True


def shiiaruraotai(hi: HandInfo) -> bool:
    """十二落抬：四副露大吊车，不能有暗杠"""
    if not hi.melds or len(hi.melds) < 4:
        return False
    for mtype, _ in hi.melds:
        if mtype == 'ankan':
            return False
    return True


def uumensai(hi: HandInfo) -> bool:
    """五门齐：包含万、筒、条、风、三元五种牌"""
    suits = set()
    
    def add_suit(tile: int):
        if tile < 27:
            suits.add(tile // 9)  # 万=0, 筒=1, 条=2
        elif tile < 31:
            suits.add(3)  # 风牌
        else:
            suits.add(4)  # 三元牌
    
    # 检查雀头
    add_suit(hi.divide.pair_tile)
    
    # 检查顺子
    for tile in hi.divide.shuntsu_first_tiles:
        add_suit(tile)
    
    # 检查刻子
    for tile in hi.divide.kotsu_tiles:
        add_suit(tile)
    
    # 检查副露
    if hi.melds:
        for mtype, tile in hi.melds:
            if mtype == 'chi':
                add_suit(tile)  # 顺子的第一张牌
            else:  # pon, kan, minkan, kakan, ankan
                add_suit(tile)
    
    return len(suits) == 5


def sanrenkou(hi: HandInfo) -> bool:
    """三连刻：三个连续数字的刻子"""
    # 收集所有刻子
    kotsu_tiles = hi.divide.kotsu_tiles[:]
    if hi.melds:
        for mtype, tile in hi.melds:
            if mtype in ('pon', 'kan', 'minkan', 'kakan', 'ankan'):
                kotsu_tiles.append(tile)
    
    if len(kotsu_tiles) < 3:
        return False
    
    # 按花色分组
    for suit in range(3):  # 万、筒、条
        suit_kotsu = []
        for tile in kotsu_tiles:
            if suit * 9 <= tile < (suit + 1) * 9:
                suit_kotsu.append(tile % 9)
        
        if len(suit_kotsu) >= 3:
            suit_kotsu.sort()
            # 检查是否有连续的三个数字
            for i in range(len(suit_kotsu) - 2):
                if suit_kotsu[i] + 1 == suit_kotsu[i + 1] and suit_kotsu[i + 1] + 1 == suit_kotsu[i + 2]:
                    return True
    
    return False


def isshokusanjun(hi: HandInfo) -> bool:
    """一色三顺：同一花色的三个相同顺子"""
    # 收集所有顺子
    shuntsu_tiles = hi.divide.shuntsu_first_tiles[:]
    if hi.melds:
        for mtype, tile in hi.melds:
            if mtype == 'chi':
                shuntsu_tiles.append(tile)
    
    if len(shuntsu_tiles) < 3:
        return False
    
    shuntsu_tiles.sort()
    
    # 检查是否有三个相同的顺子
    for i in range(len(shuntsu_tiles) - 2):
        if shuntsu_tiles[i] == shuntsu_tiles[i + 1] == shuntsu_tiles[i + 2]:
            return True
    
    # 如果有四个顺子，检查后三个
    if len(shuntsu_tiles) == 4:
        if shuntsu_tiles[1] == shuntsu_tiles[2] == shuntsu_tiles[3]:
            return True
    
    return False


# 古役满
def daisuurin(hi: HandInfo) -> bool:
    """大数邻：2-8万的七对子（门清限定）"""
    if hi.is_naki() or not hi.divide.is_chiitoi:
        return False
    return _check_all_pairs(hi.tiles34[1:8])


def daisharin(hi: HandInfo) -> bool:
    """大车轮：2-8筒的七对子（门清限定）"""
    if hi.is_naki() or not hi.divide.is_chiitoi:
        return False
    return _check_all_pairs(hi.tiles34[10:17])


def daichikurin(hi: HandInfo) -> bool:
    """大竹林：2-8条的七对子（门清限定）"""
    if hi.is_naki() or not hi.divide.is_chiitoi:
        return False
    return _check_all_pairs(hi.tiles34[19:26])


def daichisei(hi: HandInfo) -> bool:
    """大七星：字牌七对子（门清限定）"""
    if hi.is_naki() or not hi.divide.is_chiitoi:
        return False
    return _check_all_pairs(hi.tiles34[27:34])


# 老役检查器映射
OLD_YAKU_CHECKERS = {
    YakuShiiaruraotai: shiiaruraotai,
    YakuUumensai: uumensai,
    YakuSanrenkou: sanrenkou,
    YakuIsshokusanjun: isshokusanjun,
}

# 古役满检查器映射
OLD_YAKUMAN_CHECKERS = {
    YakuDaisuurin: daisuurin,
    YakuDaisharin: daisharin,
    YakuDaichikurin: daichikurin,
    YakuDaichisei: daichisei,
}


def find_old_yaku(hi: HandInfo, is_naki: bool) -> List[int]:
    """查找老役"""
    from .config import get_consider_old_yaku
    
    if not get_consider_old_yaku():
        return []
    
    old_yaku = []
    
    # 检查普通老役
    yaku_han_map = OldNakiYakuHanMap if is_naki else OldYakuHanMap
    for yaku_type in yaku_han_map:
        if yaku_type in OLD_YAKU_CHECKERS:
            if OLD_YAKU_CHECKERS[yaku_type](hi):
                old_yaku.append(yaku_type)
    
    return old_yaku


def find_old_yakuman(hi: HandInfo, is_naki: bool) -> List[int]:
    """查找古役满"""
    from .config import get_consider_old_yaku
    
    if not get_consider_old_yaku() or is_naki:
        return []
    
    old_yakuman = []
    
    # 检查古役满
    for yakuman_type in OldYakumanTimesMap:
        if yakuman_type in OLD_YAKUMAN_CHECKERS:
            if OLD_YAKUMAN_CHECKERS[yakuman_type](hi):
                old_yakuman.append(yakuman_type)
    
    return old_yakuman
