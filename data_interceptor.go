package main

import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"strings"
)

// DataInterceptor 数据拦截器
type DataInterceptor struct {
	config *ProxyConfig
}

// NewDataInterceptor 创建新的数据拦截器
func NewDataInterceptor(config *ProxyConfig) *DataInterceptor {
	return &DataInterceptor{
		config: config,
	}
}

// InterceptData 拦截并分析数据
func (d *DataInterceptor) InterceptData(data []byte) []byte {
	// 数据包大小过滤
	if len(data) < d.config.Majsoul.DataFilter.MinPacketSize {
		return nil
	}
	
	if len(data) > d.config.Majsoul.DataFilter.MaxPacketSize {
		return nil
	}
	
	// 尝试不同的数据格式解析
	if d.config.Majsoul.DataFilter.EnableWebSocketParsing {
		if payload := d.extractWebSocketPayload(data); payload != nil {
			return payload
		}
	}
	
	if d.config.Majsoul.DataFilter.EnableJSONParsing {
		if d.isJSONData(data) {
			return data
		}
	}
	
	if d.config.Majsoul.DataFilter.EnableProtobufParsing {
		if d.isProtobufData(data) {
			return data
		}
	}
	
	return nil
}

// isWebSocketFrame 检查是否为WebSocket帧
func (d *DataInterceptor) isWebSocketFrame(data []byte) bool {
	if len(data) < 2 {
		return false
	}
	
	// WebSocket帧格式检查
	fin := (data[0] & 0x80) != 0
	opcode := data[0] & 0x0f
	masked := (data[1] & 0x80) != 0
	
	// 基本格式验证
	// opcode: 0x1=text, 0x2=binary, 0x8=close, 0x9=ping, 0xa=pong
	validOpcodes := []byte{0x0, 0x1, 0x2, 0x8, 0x9, 0xa}
	isValidOpcode := false
	for _, validOpcode := range validOpcodes {
		if opcode == validOpcode {
			isValidOpcode = true
			break
		}
	}
	
	if !isValidOpcode {
		return false
	}
	
	// 检查载荷长度字段的合理性
	payloadLen := data[1] & 0x7f
	headerLen := 2
	
	if payloadLen == 126 {
		if len(data) < 4 {
			return false
		}
		headerLen = 4
	} else if payloadLen == 127 {
		if len(data) < 10 {
			return false
		}
		headerLen = 10
	}
	
	if masked {
		headerLen += 4
	}
	
	return len(data) >= headerLen
}

// extractWebSocketPayload 提取WebSocket载荷
func (d *DataInterceptor) extractWebSocketPayload(data []byte) []byte {
	if !d.isWebSocketFrame(data) {
		return nil
	}
	
	if len(data) < 2 {
		return nil
	}
	
	payloadLen := int(data[1] & 0x7f)
	headerLen := 2
	
	// 处理扩展载荷长度
	if payloadLen == 126 {
		if len(data) < 4 {
			return nil
		}
		payloadLen = int(binary.BigEndian.Uint16(data[2:4]))
		headerLen = 4
	} else if payloadLen == 127 {
		if len(data) < 10 {
			return nil
		}
		payloadLen64 := binary.BigEndian.Uint64(data[2:10])
		if payloadLen64 > 0x7FFFFFFF { // 避免整数溢出
			return nil
		}
		payloadLen = int(payloadLen64)
		headerLen = 10
	}
	
	// 检查掩码
	masked := (data[1] & 0x80) != 0
	var maskKey []byte
	
	if masked {
		if len(data) < headerLen+4 {
			return nil
		}
		maskKey = data[headerLen : headerLen+4]
		headerLen += 4
	}
	
	// 检查数据长度
	if len(data) < headerLen+payloadLen {
		return nil
	}
	
	// 提取载荷
	payload := make([]byte, payloadLen)
	copy(payload, data[headerLen:headerLen+payloadLen])
	
	// 如果有掩码，进行解码
	if masked && maskKey != nil {
		for i := 0; i < payloadLen; i++ {
			payload[i] ^= maskKey[i%4]
		}
	}
	
	return payload
}

// isJSONData 检查是否为JSON数据
func (d *DataInterceptor) isJSONData(data []byte) bool {
	// 去除前后空白字符
	trimmed := bytes.TrimSpace(data)
	if len(trimmed) == 0 {
		return false
	}
	
	// JSON数据应该以 { 或 [ 开始
	if trimmed[0] != '{' && trimmed[0] != '[' {
		return false
	}
	
	// 尝试解析JSON
	var jsonData interface{}
	if err := json.Unmarshal(trimmed, &jsonData); err != nil {
		return false
	}
	
	// 检查是否包含雀魂相关字段
	jsonStr := string(trimmed)
	majsoulKeywords := []string{
		"account_id",
		"tiles",
		"majsoul",
		"liqipai",
		"ActionPrototype",
		"GameMode",
		"seat",
		"dora",
		"hai",
	}
	
	for _, keyword := range majsoulKeywords {
		if strings.Contains(jsonStr, keyword) {
			return true
		}
	}
	
	return false
}

// isProtobufData 检查是否为Protobuf数据
func (d *DataInterceptor) isProtobufData(data []byte) bool {
	if len(data) < 4 {
		return false
	}
	
	// Protobuf数据通常以特定的字节模式开始
	// 这里实现一个简单的启发式检测
	
	// 检查是否有合理的varint编码
	if !d.hasValidVarint(data) {
		return false
	}
	
	// 检查字段标签的合理性
	if !d.hasValidFieldTags(data) {
		return false
	}
	
	return true
}

// hasValidVarint 检查是否包含有效的varint编码
func (d *DataInterceptor) hasValidVarint(data []byte) bool {
	for i := 0; i < len(data) && i < 10; i++ {
		if data[i]&0x80 == 0 {
			return true // 找到varint结束标志
		}
	}
	return false
}

// hasValidFieldTags 检查是否包含有效的字段标签
func (d *DataInterceptor) hasValidFieldTags(data []byte) bool {
	// Protobuf字段标签的格式：(field_number << 3) | wire_type
	// wire_type: 0=varint, 1=64bit, 2=length-delimited, 5=32bit
	
	for i := 0; i < len(data)-1; i++ {
		tag := data[i]
		wireType := tag & 0x07
		fieldNumber := tag >> 3
		
		// 检查wire type是否有效
		if wireType <= 5 && wireType != 3 && wireType != 4 && fieldNumber > 0 {
			return true
		}
	}
	
	return false
}

// IsGameData 检查是否为游戏数据
func (d *DataInterceptor) IsGameData(data []byte) bool {
	// 基本长度检查
	if len(data) < d.config.Majsoul.DataFilter.MinPacketSize {
		return false
	}
	
	// WebSocket帧检查
	if d.config.Majsoul.DataFilter.EnableWebSocketParsing && d.isWebSocketFrame(data) {
		payload := d.extractWebSocketPayload(data)
		if payload != nil && len(payload) > 0 {
			return d.isGameDataPayload(payload)
		}
	}
	
	// 直接数据检查
	return d.isGameDataPayload(data)
}

// isGameDataPayload 检查载荷是否为游戏数据
func (d *DataInterceptor) isGameDataPayload(data []byte) bool {
	// JSON数据检查
	if d.config.Majsoul.DataFilter.EnableJSONParsing && d.isJSONData(data) {
		return true
	}
	
	// Protobuf数据检查
	if d.config.Majsoul.DataFilter.EnableProtobufParsing && d.isProtobufData(data) {
		return true
	}
	
	// 其他特征检查
	dataStr := string(data)
	
	// 检查常见的游戏数据特征
	gameFeatures := []string{
		"ActionPrototype",
		"GameMode",
		"seat",
		"tiles",
		"dora",
		"hai",
		"account_id",
		"uuid",
		"round",
		"wind",
		"score",
	}
	
	featureCount := 0
	for _, feature := range gameFeatures {
		if strings.Contains(dataStr, feature) {
			featureCount++
		}
	}
	
	// 如果包含多个游戏特征，认为是游戏数据
	return featureCount >= 2
}

// GetDataType 获取数据类型
func (d *DataInterceptor) GetDataType(data []byte) string {
	if d.isWebSocketFrame(data) {
		payload := d.extractWebSocketPayload(data)
		if payload != nil {
			if d.isJSONData(payload) {
				return "WebSocket-JSON"
			} else if d.isProtobufData(payload) {
				return "WebSocket-Protobuf"
			} else {
				return "WebSocket-Binary"
			}
		}
		return "WebSocket-Unknown"
	}
	
	if d.isJSONData(data) {
		return "JSON"
	}
	
	if d.isProtobufData(data) {
		return "Protobuf"
	}
	
	return "Binary"
}

// PrintDataInfo 打印数据信息（调试用）
func (d *DataInterceptor) PrintDataInfo(data []byte) {
	fmt.Printf("数据长度: %d bytes\n", len(data))
	fmt.Printf("数据类型: %s\n", d.GetDataType(data))
	fmt.Printf("是否为游戏数据: %t\n", d.IsGameData(data))
	
	if len(data) > 0 {
		fmt.Printf("前16字节: %x\n", data[:min(16, len(data))])
		
		// 如果是可打印字符，显示文本内容
		if d.isPrintableText(data[:min(100, len(data))]) {
			fmt.Printf("文本内容: %s\n", string(data[:min(100, len(data))]))
		}
	}
}

// isPrintableText 检查是否为可打印文本
func (d *DataInterceptor) isPrintableText(data []byte) bool {
	printableCount := 0
	for _, b := range data {
		if (b >= 32 && b <= 126) || b == 9 || b == 10 || b == 13 {
			printableCount++
		}
	}
	
	return float64(printableCount)/float64(len(data)) > 0.8
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
