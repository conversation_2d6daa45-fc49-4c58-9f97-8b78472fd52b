from __future__ import annotations
from typing import List, Tuple
from .yaku_data import *


def yakuman_list(tiles34: List[int], win_tile: int, pair_tile: int, shuntsu_first: List[int], kotsu_tiles: List[int], is_chiitoi: bool, melds: List[Tuple[str,int]]|None=None) -> List[int]:
    y: List[int] = []
    # 四暗刻 / 四暗刻单骑
    ankou_count = len(kotsu_tiles)
    if ankou_count == 4:
        if win_tile == pair_tile:
            y.append(YakuSuuAnkouTanki)
        else:
            y.append(YakuSuuAnkou)
    # 大三元
    if set([31, 32, 33]).issubset(set(kotsu_tiles)):
        y.append(YakuDaisangen)
    # 小四喜/大四喜
    winds = [27, 28, 29, 30]
    wind_kotsu = sum(1 for t in kotsu_tiles if t in winds)
    if wind_kotsu == 4:
        y.append(YakuDaisuush<PERSON>)
    elif wind_kotsu == 3 and pair_tile in winds:
        y.append(Ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
    # 字一色
    if is_chiitoi:
        if all(tiles34[i] > 0 for i in range(27, 34)):
            y.append(YakuTsuuiisou)
    else:
        if pair_tile >= 27 and len(shuntsu_first) == 0 and all(t >= 27 for t in kotsu_tiles):
            y.append(YakuTsuuiisou)
    # 清老头
    if (not is_chiitoi) and len(shuntsu_first) == 0 and all((t < 27 and (t % 9 in (0, 8))) for t in kotsu_tiles):
        t9 = pair_tile % 9
        if pair_tile < 27 and t9 in (0, 8):
            y.append(YakuChinroutou)
    # 绿一色
    ryuu = {19, 20, 21, 23, 25, 32}
    if not is_chiitoi:
        if all((t in ryuu) for t in kotsu_tiles) and all((s == 19) for s in shuntsu_first) and pair_tile in ryuu:
            y.append(YakuRyuuiisou)
    # 九莲
    for base in (0, 9, 18):
        block = tiles34[base:base+9]
        if sum(block) == 14 and sum(tiles34[27:]) == 0:
            req = [3,1,1,1,1,1,1,1,3]
            ok = True
            extra_idx = -1
            for i,c in enumerate(block):
                need = req[i]
                if c < need:
                    ok = False; break
                if c > need:
                    if extra_idx != -1:
                        ok = False; break
                    extra_idx = base + i
            if ok:
                y.append(YakuChuuren9 if win_tile == extra_idx else YakuChuuren)
    # 四杠子（需副露）
    if melds:
        kan_count = sum(1 for mtype,_ in melds if mtype in ('ankan','minkan','kakan'))
        if kan_count >= 4:
            y.append(YakuSuuKantsu)

    # 古役满
    from .config import get_consider_old_yaku
    if get_consider_old_yaku():
        from .yaku_old import find_old_yakuman
        from .yaku import HandInfo
        from .divide import DivideResult

        # 构造HandInfo对象来检查古役满
        divide_result = DivideResult(
            pair_tile=pair_tile,
            shuntsu_first_tiles=shuntsu_first,
            kotsu_tiles=kotsu_tiles,
            is_chiitoi=is_chiitoi,
            is_ryanpeikou=False,
            is_iipeikou=False,
            is_ittsuu=False
        )

        hi = HandInfo(
            tiles34=tiles34,
            divide=divide_result,
            melds=melds or [],
            win_tile=win_tile,
            is_tsumo=False,
            is_riichi=False,
            is_daburii=False
        )

        old_yakuman = find_old_yakuman(hi, melds is not None and len(melds) > 0)
        y.extend(old_yakuman)

    # 天和地和
    from .config import get_tenhou_chiihou
    is_tenhou, is_chiihou = get_tenhou_chiihou()
    if is_tenhou:
        y.append(YakuTenhou)
    elif is_chiihou:
        y.append(YakuChiihou)

    return y