from mahjong_py import str_to_tiles34, calculate_shanten
from mahjong_py.shanten_improves import _calc_waits13, _default_left, ALL_TILES

# 测试_calc_waits13函数
print("Testing _calc_waits13...")

# 测试用例：听牌状态
tiles34, _ = str_to_tiles34("123m 456m 789m 123p 1z")  # 13张，听1z
print(f"tiles34: {tiles34}")
print(f"sum: {sum(tiles34)}")

left = _default_left(tiles34)
print(f"left tiles initialized")

base_shanten = calculate_shanten(tiles34)
print(f"base shanten: {base_shanten}")

# 手动测试进张检查
print("Manual wait checking:")
for t in [27]:  # 只测试1z (东)
    if left[t] == 0:
        print(f"Tile {t}: no tiles left")
        continue
    
    print(f"Testing tile {t}...")
    tiles34[t] += 1
    print(f"After adding tile {t}, tiles34 sum: {sum(tiles34)}")
    
    try:
        new_shanten = calculate_shanten(tiles34)
        print(f"New shanten: {new_shanten}, base: {base_shanten}")
        if new_shanten < base_shanten:
            print(f"Tile {t} is a wait! Left: {left[t]}")
        else:
            print(f"Tile {t} is not a wait")
    except Exception as e:
        print(f"Error calculating shanten for tile {t}: {e}")
        break
    
    tiles34[t] -= 1

print("Now testing _calc_waits13...")
try:
    waits = _calc_waits13(tiles34, left)
    print(f"Waits: {waits.counts}")
    print(f"Total waits: {waits.all_count()}")
except Exception as e:
    print(f"Error in _calc_waits13: {e}")
