from mahjong_py import str_to_tiles34, calculate_shanten, is_agari

# 验证测试用例的向听数
test_cases = [
    "123456789m 1234s",  # 应该是听牌（0向听）
    "123456789m 12344s", # 应该是和牌（-1向听）
    "11223344556677z",   # 七对子和牌
    "1133556699m 1122s", # 七对子和牌
    "111222333m 119p",   # 复杂听牌
]

for hand_str in test_cases:
    tiles34, _ = str_to_tiles34(hand_str)
    tile_count = sum(tiles34)
    shanten = calculate_shanten(tiles34)
    agari = is_agari(tiles34)
    
    print(f"手牌: {hand_str}")
    print(f"牌数: {tile_count}")
    print(f"向听数: {shanten}")
    print(f"和牌: {agari}")
    print(f"tiles34: {tiles34}")
    print("-" * 50)
