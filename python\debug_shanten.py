from mahjong_py import str_to_tiles34, calculate_shanten

# 简单测试
print("Testing simple cases...")

# 测试1：简单的和牌
tiles34, _ = str_to_tiles34("123m 456m 789m 123p 11z")
print(f"Test 1 - tiles34: {tiles34}")
print(f"Test 1 - sum: {sum(tiles34)}")

try:
    shanten = calculate_shanten(tiles34)
    print(f"Test 1 - shanten: {shanten}")
except Exception as e:
    print(f"Test 1 - Error: {e}")

# 测试2：更简单的情况
tiles34_simple = [0] * 34
tiles34_simple[0] = 3  # 111m
tiles34_simple[3] = 3  # 444m  
tiles34_simple[6] = 3  # 777m
tiles34_simple[9] = 3  # 111p
tiles34_simple[27] = 2  # 11z (东东)

print(f"Test 2 - tiles34: {tiles34_simple}")
print(f"Test 2 - sum: {sum(tiles34_simple)}")

try:
    shanten = calculate_shanten(tiles34_simple)
    print(f"Test 2 - shanten: {shanten}")
except Exception as e:
    print(f"Test 2 - Error: {e}")

# 测试3：最简单的情况 - 只有一张牌
tiles34_minimal = [0] * 34
tiles34_minimal[0] = 1  # 1m

print(f"Test 3 - tiles34: {tiles34_minimal}")
print(f"Test 3 - sum: {sum(tiles34_minimal)}")

try:
    shanten = calculate_shanten(tiles34_minimal)
    print(f"Test 3 - shanten: {shanten}")
except Exception as e:
    print(f"Test 3 - Error: {e}")
