from mahjong_py import str_to_tiles34, calc_point_hand
from mahjong_py.config import set_consider_old_yaku, set_tenhou_chiihou
from mahjong_py.yaku import HandInfo, yaku_normal
from mahjong_py.yakuman import yakuman_list
from mahjong_py.divide import divide_tiles34
from mahjong_py.yaku_old import *
import copy

# 测试役满和老役系统
print("Testing Yakuman and Old Yaku system...")

# 启用老役
set_consider_old_yaku(True)
print("Old yaku enabled")

# 测试1：四暗刻
print("\n=== Test 1: 四暗刻 ===")
tiles34, _ = str_to_tiles34("111m 222p 333s 444z 55z")
divide_results = divide_tiles34(tiles34)
if divide_results:
    divide_result = divide_results[0]  # 取第一个结果
    yakuman = yakuman_list(tiles34, 31, divide_result.pair_tile,
                          divide_result.shuntsu_first_tiles,
                          divide_result.kotsu_tiles,
                          divide_result.is_chiitoi)
    print(f"Yaku<PERSON> found: {yakuman}")
    if yakuman:
        point = calc_point_hand(tiles34, 31, is_tsumo=False, is_parent=False,
                               round_wind=27, self_wind=28)
        print(f"Point: {point}")

# 测试2：大三元
print("\n=== Test 2: 大三元 ===")
tiles34, _ = str_to_tiles34("111m 222p 333z 444z 555z")
divide_results = divide_tiles34(tiles34)
if divide_results:
    divide_result = divide_results[0]
    yakuman = yakuman_list(tiles34, 0, divide_result.pair_tile,
                          divide_result.shuntsu_first_tiles,
                          divide_result.kotsu_tiles,
                          divide_result.is_chiitoi)
    print(f"Yakuman found: {yakuman}")

# 测试3：字一色
print("\n=== Test 3: 字一色 ===")
tiles34, _ = str_to_tiles34("111z 222z 333z 444z 55z")
divide_results = divide_tiles34(tiles34)
if divide_results:
    divide_result = divide_results[0]
    yakuman = yakuman_list(tiles34, 27, divide_result.pair_tile,
                          divide_result.shuntsu_first_tiles,
                          divide_result.kotsu_tiles,
                          divide_result.is_chiitoi)
    print(f"Yakuman found: {yakuman}")

# 测试4：古役满 - 大数邻（2-8万七对子）
print("\n=== Test 4: 古役满 - 大数邻 ===")
tiles34 = [0] * 34
for i in range(1, 8):  # 2-8万
    tiles34[i] = 2
divide_results = divide_tiles34(tiles34)
if divide_results:
    divide_result = divide_results[0]
    yakuman = yakuman_list(tiles34, 4, divide_result.pair_tile,
                          divide_result.shuntsu_first_tiles,
                          divide_result.kotsu_tiles,
                          divide_result.is_chiitoi)
    print(f"Old yakuman found: {yakuman}")

# 测试5：古役满 - 大七星（字牌七对子）
print("\n=== Test 5: 古役满 - 大七星 ===")
tiles34 = [0] * 34
for i in range(27, 34):  # 字牌
    tiles34[i] = 2
divide_results = divide_tiles34(tiles34)
if divide_results:
    divide_result = divide_results[0]
    yakuman = yakuman_list(tiles34, 27, divide_result.pair_tile,
                          divide_result.shuntsu_first_tiles,
                          divide_result.kotsu_tiles,
                          divide_result.is_chiitoi)
    print(f"Old yakuman found: {yakuman}")

# 测试6：老役 - 五门齐
print("\n=== Test 6: 老役 - 五门齐 ===")
tiles34, _ = str_to_tiles34("123m 456p 789s 111z 22z")  # 万筒条风三元

divide_results = divide_tiles34(tiles34)
if divide_results:
    hi = HandInfo(
        tiles34=tiles34,
        divide=divide_results[0],
        melds=[],
        win_tile=27,
        is_tsumo=False,
        is_riichi=False,
        is_daburii=False
    )
    hi.round_wind = 27
    hi.self_wind = 28

    yaku = yaku_normal(hi)
    print(f"Yaku found: {yaku}")

    # 检查是否包含五门齐
    from mahjong_py.yaku_data import YakuUumensai
    if YakuUumensai in yaku:
        print("五门齐 detected!")
        point = calc_point_hand(tiles34, 27, is_tsumo=False, is_parent=False,
                               round_wind=27, self_wind=28)
        print(f"Point: {point}")

# 测试7：老役 - 三连刻
print("\n=== Test 7: 老役 - 三连刻 ===")
tiles34, _ = str_to_tiles34("111m 222m 333m 456p 77z")  # 123万连刻

divide_results = divide_tiles34(tiles34)
if divide_results:
    hi = HandInfo(
        tiles34=tiles34,
        divide=divide_results[0],
        melds=[],
        win_tile=30,
        is_tsumo=False,
        is_riichi=False,
        is_daburii=False
    )
    hi.round_wind = 27
    hi.self_wind = 28

    yaku = yaku_normal(hi)
    print(f"Yaku found: {yaku}")

    # 检查是否包含三连刻
    from mahjong_py.yaku_data import YakuSanrenkou
    if YakuSanrenkou in yaku:
        print("三连刻 detected!")

# 测试8：老役 - 一色三顺
print("\n=== Test 8: 老役 - 一色三顺 ===")
tiles34, _ = str_to_tiles34("123m 123m 123m 456p 77z")  # 三个123万顺子

divide_results = divide_tiles34(tiles34)
if divide_results:
    hi = HandInfo(
        tiles34=tiles34,
        divide=divide_results[0],
        melds=[],
        win_tile=30,
        is_tsumo=False,
        is_riichi=False,
        is_daburii=False
    )
    hi.round_wind = 27
    hi.self_wind = 28

    yaku = yaku_normal(hi)
    print(f"Yaku found: {yaku}")

    # 检查是否包含一色三顺
    from mahjong_py.yaku_data import YakuIsshokusanjun
    if YakuIsshokusanjun in yaku:
        print("一色三顺 detected!")

# 测试9：天和地和
print("\n=== Test 9: 天和地和 ===")
set_tenhou_chiihou(True, False)  # 设置天和
tiles34, _ = str_to_tiles34("123m 456m 789m 123p 11z")
divide_results = divide_tiles34(tiles34)
if divide_results:
    divide_result = divide_results[0]
    yakuman = yakuman_list(tiles34, 27, divide_result.pair_tile,
                          divide_result.shuntsu_first_tiles,
                          divide_result.kotsu_tiles,
                          divide_result.is_chiitoi)
    print(f"Tenhou yakuman found: {yakuman}")
    from mahjong_py.yaku_data import YakuTenhou
    if YakuTenhou in yakuman:
        print("天和 detected!")

# 重置天和地和标志
set_tenhou_chiihou(False, False)

print("\nYakuman and Old Yaku system test completed!")
