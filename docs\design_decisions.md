# 设计决策

## 代理模式对比

### 1. HTTP代理 vs SOCKS代理 vs 透明代理

| 方案 | 优点 | 缺点 | 适用性 |
|------|------|------|--------|
| HTTP CONNECT | 简单实现，支持HTTPS | 需要客户端支持 | ✅ 适合 |
| SOCKS5 | 协议完整，功能强大 | 实现复杂 | ⚠️ 过度设计 |
| 透明代理 | 完全透明 | 需要系统级权限 | ❌ 部署复杂 |

### 2. 数据拦截策略

**方案A: SSL中间人攻击**
```python
# 需要证书管理，复杂度高
ssl_context = ssl.create_default_context()
ssl_context.load_cert_chain('proxy.crt', 'proxy.key')
```

**方案B: 流量模式识别** ✅
```python
# 基于数据包特征识别
def is_majsoul_data(data):
    # WebSocket帧特征
    # JSON数据特征
    # protobuf特征
    return pattern_match(data)
```

**选择理由**: 方案B实现简单，无需处理证书，满足基本需求