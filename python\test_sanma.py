from mahjong_py import str_to_tiles34, calculate_shanten, analyze_13, analyze_14
from mahjong_py.avg_point import calc_avg_point, calc_avg_riichi_point
from mahjong_py.agari_rate import calculate_agari_rate_each_tile
from mahjong_py.risk import calculate_risk_tiles34, mixed_risk_table
from mahjong_py.point import calc_point_hand
from mahjong_py.dora import dora_list
import copy

# 测试三麻功能
print("Testing Sanma (3-player mahjong) functionality...")

# 测试用例：三麻手牌（不包含2-8m）
tiles34, _ = str_to_tiles34("19m 123p 456p 789s 11z")  # 13张
print(f"Sanma tiles34: {tiles34}")
print(f"sum: {sum(tiles34)}")

# 测试基础向听数计算（三麻和四麻应该相同）
shanten_4ma = calculate_shanten(tiles34)
print(f"4-player shanten: {shanten_4ma}")

# 测试三麻分析
r13_sanma = analyze_13(tiles34, is_sanma=True)
print(f"Sanma shanten: {r13_sanma.shanten}")
print(f"Sanma waits: {r13_sanma.waits.counts}")
print(f"Sanma total waits: {r13_sanma.waits.all_count()}")

# 对比四麻分析
r13_4ma = analyze_13(tiles34, is_sanma=False)
print(f"4-player waits: {r13_4ma.waits.counts}")
print(f"4-player total waits: {r13_4ma.waits.all_count()}")

# 测试14张分析
tiles34_14, _ = str_to_tiles34("19m 123p 456p 789s 111z")  # 14张
print(f"\n14-tile analysis:")
print(f"tiles34_14: {tiles34_14}")

ms_sanma, rlist_sanma = analyze_14(copy.deepcopy(tiles34_14), is_sanma=True)
print(f"Sanma 14-tile min shanten: {ms_sanma}")
if rlist_sanma:
    print(f"Sanma top discard: {rlist_sanma[0].discard_tile}, waits: {rlist_sanma[0].result13.waits.all_count()}")

ms_4ma, rlist_4ma = analyze_14(copy.deepcopy(tiles34_14), is_sanma=False)
print(f"4-player 14-tile min shanten: {ms_4ma}")
if rlist_4ma:
    print(f"4-player top discard: {rlist_4ma[0].discard_tile}, waits: {rlist_4ma[0].result13.waits.all_count()}")

# 测试和率计算
print(f"\nTesting agari rate calculation:")
rates_sanma = calculate_agari_rate_each_tile(r13_sanma.waits.counts)
rates_4ma = calculate_agari_rate_each_tile(r13_4ma.waits.counts)
print(f"Sanma rates: {rates_sanma}")
print(f"4-player rates: {rates_4ma}")

# 测试平均点数计算
avg_pt_sanma, _ = calc_avg_point(
    tiles34[:], r13_sanma.waits.counts,
    is_parent=False, is_riichi=False, is_tsumo=False,
    round_wind=27, self_wind=27
)
print(f"Sanma avg point: {avg_pt_sanma}")

# 测试Dora计算（三麻特殊规则：1m->9m）
print(f"\nTesting Dora calculation:")
dora_indicators = [0]  # 1m作为dora指示牌
dora_tiles_sanma = dora_list(dora_indicators, is_sanma=True)
dora_tiles_4ma = dora_list(dora_indicators, is_sanma=False)
print(f"Dora indicator: 1m")
print(f"Sanma dora tiles: {dora_tiles_sanma}")  # 应该是9m
print(f"4-player dora tiles: {dora_tiles_4ma}")  # 应该是2m

# 测试点数计算（包含dora）
test_agari_tiles, _ = str_to_tiles34("111m 123p 456p 789s 11z")  # 和牌
pt_sanma = calc_point_hand(test_agari_tiles, 0, is_tsumo=False, is_parent=False,
                           round_wind=27, self_wind=27, dora_indicators=dora_indicators, is_sanma=True)
pt_4ma = calc_point_hand(test_agari_tiles, 0, is_tsumo=False, is_parent=False,
                         round_wind=27, self_wind=27, dora_indicators=dora_indicators, is_sanma=False)
print(f"Sanma point (with dora): {pt_sanma}")
print(f"4-player point (with dora): {pt_4ma}")

# 测试风险计算
print(f"\nTesting risk calculation:")
safe_tiles = [False] * 34
left_tiles = [4] * 34
# 三麻时2-8m应该没有剩余牌
if True:  # 模拟三麻环境
    for i in range(1, 8):  # 2-8m
        left_tiles[i] = 0

risk_sanma = calculate_risk_tiles34(5, safe_tiles, left_tiles, [], 27, 28, is_sanma=True)
risk_4ma = calculate_risk_tiles34(5, safe_tiles, left_tiles, [], 27, 28, is_sanma=False)

print(f"Sanma risk for 2m (idx=1): {risk_sanma.values[1]}")  # 应该是0
print(f"4-player risk for 2m (idx=1): {risk_4ma.values[1]}")  # 应该>0
print(f"Sanma risk for 1m (idx=0): {risk_sanma.values[0]}")  # 应该>0
print(f"4-player risk for 1m (idx=0): {risk_4ma.values[0]}")  # 应该>0

print("\nSanma functionality test completed!")
