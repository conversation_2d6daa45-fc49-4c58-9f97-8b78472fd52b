from mahjong_py import str_to_tiles34, calculate_shanten, analyze_13, analyze_14
from mahjong_py.config import set_consider_old_yaku, set_is_sanma
from mahjong_py.avg_point import calc_avg_point, calc_avg_riichi_point
from mahjong_py.agari_rate import calculate_agari_rate_each_tile
from mahjong_py.risk import calculate_risk_tiles34
from mahjong_py.point import calc_point_hand
import copy

print("=== 日本麻将助手 Python版 综合功能测试 ===\n")

# 测试1：基础向听数和进张分析
print("1. 基础向听数和进张分析")
tiles34, _ = str_to_tiles34("123m 456m 78m 234p 56s")  # 13张，1向听
print(f"手牌: 123m 456m 78m 234p 56s")
print(f"向听数: {calculate_shanten(tiles34)}")

r13 = analyze_13(tiles34)
print(f"进张: {r13.waits.counts}")
print(f"总进张数: {r13.waits.all_count()}")
print(f"综合评分: {r13.mixed_waits_score:.2f}")
print()

# 测试2：14张何切分析
print("2. 14张何切分析")
tiles34_14, _ = str_to_tiles34("123m 456m 789m 234p 56s 1z")  # 14张
print(f"手牌: 123m 456m 789m 234p 56s 1z")
print(f"牌数检查: {sum(tiles34_14)}")

if sum(tiles34_14) == 14:
    min_s, results = analyze_14(copy.deepcopy(tiles34_14))
    print(f"最小向听数: {min_s}")
    print("推荐弃牌:")
    for i, r in enumerate(results[:3]):
        tile_name = f"{r.discard_tile//9+1}{['m','p','s','z'][r.discard_tile//9]}" if r.discard_tile < 27 else f"{r.discard_tile-26}z"
        print(f"  {i+1}. 弃{tile_name}: 进张{r.result13.waits.all_count()}, 评分{r.result13.mixed_waits_score:.2f}")
else:
    print(f"错误：牌数不是14张，实际{sum(tiles34_14)}张")
print()

# 测试3：平均点数和和率计算
print("3. 平均点数和和率计算")
tiles34_tenpai, _ = str_to_tiles34("123m 456m 789m 123p 1z")
print(f"听牌手牌: 123m 456m 789m 123p 1z")

r13_tenpai = analyze_13(tiles34_tenpai)
print(f"听牌: {r13_tenpai.waits.counts}")

# 普通和率
rates = calculate_agari_rate_each_tile(r13_tenpai.waits.counts)
print(f"各牌和率: {rates}")

# 平均点数
avg_pt, _ = calc_avg_point(
    tiles34_tenpai[:], r13_tenpai.waits.counts,
    is_parent=False, is_riichi=False, is_tsumo=False,
    round_wind=27, self_wind=28
)
print(f"平均点数: {avg_pt:.0f}")

# 立直时平均点数
riichi_avg_pt, _ = calc_avg_riichi_point(
    tiles34_tenpai[:], r13_tenpai.waits.counts,
    is_parent=False, round_wind=27, self_wind=28
)
print(f"立直平均点数: {riichi_avg_pt:.0f}")
print(f"立直奖励: {riichi_avg_pt - avg_pt:.0f}点")
print()

# 测试4：三麻功能
print("4. 三麻功能测试")
set_is_sanma(True)
tiles34_sanma, _ = str_to_tiles34("19m 123p 456p 789s 11z")
print(f"三麻手牌: 19m 123p 456p 789s 11z")

r13_sanma = analyze_13(tiles34_sanma, is_sanma=True)
r13_4ma = analyze_13(tiles34_sanma, is_sanma=False)

print(f"三麻进张数: {r13_sanma.waits.all_count()}")
print(f"四麻进张数: {r13_4ma.waits.all_count()}")

# 风险计算
safe_tiles = [False] * 34
left_tiles = [4] * 34
# 三麻时2-8m没有剩余牌
for i in range(1, 8):
    left_tiles[i] = 0

risk_sanma = calculate_risk_tiles34(5, safe_tiles, left_tiles, [], 27, 28, is_sanma=True)
risk_4ma = calculate_risk_tiles34(5, safe_tiles, left_tiles, [], 27, 28, is_sanma=False)

print(f"2m风险 - 三麻: {risk_sanma.values[1]:.1f}%, 四麻: {risk_4ma.values[1]:.1f}%")
print()

# 测试5：老役系统
print("5. 老役系统测试")
set_consider_old_yaku(True)

# 五门齐测试
tiles34_uumensai, _ = str_to_tiles34("123m 456p 789s 111z 22z")
print(f"五门齐手牌: 123m 456p 789s 111z 22z")
point_uumensai = calc_point_hand(tiles34_uumensai, 27, is_tsumo=False, is_parent=False,
                                round_wind=27, self_wind=28)
print(f"点数: {point_uumensai}")

# 三连刻测试
tiles34_sanrenkou, _ = str_to_tiles34("111m 222m 333m 456p 77z")
print(f"三连刻手牌: 111m 222m 333m 456p 77z")
point_sanrenkou = calc_point_hand(tiles34_sanrenkou, 30, is_tsumo=False, is_parent=False,
                                 round_wind=27, self_wind=28)
print(f"点数: {point_sanrenkou}")
print()

# 测试6：役满系统
print("6. 役满系统测试")

# 四暗刻
tiles34_suuankou, _ = str_to_tiles34("111m 222p 333s 444z 55z")
print(f"四暗刻手牌: 111m 222p 333s 444z 55z")
point_suuankou = calc_point_hand(tiles34_suuankou, 31, is_tsumo=False, is_parent=False,
                                round_wind=27, self_wind=28)
print(f"点数: {point_suuankou}")

# 字一色
tiles34_tsuuiisou, _ = str_to_tiles34("111z 222z 333z 444z 55z")
print(f"字一色手牌: 111z 222z 333z 444z 55z")
point_tsuuiisou = calc_point_hand(tiles34_tsuuiisou, 27, is_tsumo=False, is_parent=False,
                                 round_wind=27, self_wind=28)
print(f"点数: {point_tsuuiisou}")
print()

print("=== 综合功能测试完成 ===")
print("✓ 向听数计算和进张分析")
print("✓ 何切推荐和综合评分")
print("✓ 平均点数和和率计算")
print("✓ 立直里宝加成")
print("✓ 三麻联动功能")
print("✓ 老役系统")
print("✓ 役满系统")
print("\n日本麻将助手 Python版 核心功能已完善！")
