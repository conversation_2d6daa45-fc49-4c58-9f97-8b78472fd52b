# 数据流设计

## 连接建立流程

```mermaid
sequenceDiagram
    participant C as 雀魂客户端
    participant P as Proxifier
    participant PS as 代理服务器
    participant MS as 雀魂服务器
    participant MH as 麻将助手

    C->>P: 发起连接
    P->>PS: CONNECT majsoul.com:443
    PS->>MS: 建立TCP连接
    MS-->>PS: 连接成功
    PS-->>P: HTTP/1.1 200 Connection established
    P-->>C: 连接建立

    Note over PS,MS: SSL握手（透传）
    C->>PS: SSL Client Hello
    PS->>MS: 转发
    MS-->>PS: SSL Server Hello
    PS-->>C: 转发

    Note over C,MS: 应用数据传输
    loop 游戏数据
        C->>PS: 游戏请求
        PS->>MS: 转发
        MS-->>PS: 游戏响应
        PS->>MH: 发送副本（如果是雀魂数据）
        PS-->>C: 转发响应
    end
```

## 数据识别策略

### 1. 主机名识别
```python
majsoul_hosts = [
    "majsoul.com",
    "maj-soul.com", 
    "mahjongsoul.game.yo-star.com",
    "game.mahjongsoul.com"
]
```

### 2. 数据包特征识别
```python
def is_game_data(data):
    # WebSocket帧头
    if len(data) >= 2 and (data[0] & 0x80):
        return True
    
    # JSON数据特征
    try:
        json_str = data.decode('utf-8')
        if 'account_id' in json_str or 'tiles' in json_str:
            return True
    except:
        pass
    
    return False
```