# 配置系统设计

## 配置文件结构

```json
{
    "proxy": {
        "host": "127.0.0.1",
        "port": 8888,
        "max_connections": 100,
        "connection_timeout": 10
    },
    "mahjong_helper": {
        "url": "http://localhost:12121/majsoul-client",
        "timeout": 1.0,
        "retry_count": 3,
        "retry_delay": 0.5
    },
    "logging": {
        "level": "INFO",
        "file": "proxy.log",
        "max_size": "10MB",
        "backup_count": 5
    },
    "majsoul": {
        "hosts": [
            "majsoul.com",
            "maj-soul.com",
            "mahjongsoul.game.yo-star.com",
            "game.mahjongsoul.com"
        ],
        "ports": [443, 4130, 4131, 4132, 4133, 4134, 4135],
        "data_filter": {
            "min_packet_size": 10,
            "max_packet_size": 65536,
            "enable_websocket_parsing": true,
            "enable_json_parsing": true,
            "enable_protobuf_parsing": false
        }
    },
    "advanced": {
        "buffer_size": 8192,
        "keep_alive": true,
        "tcp_nodelay": true
    }
}
```

## 配置管理类

```python
class ProxyConfig:
    def __init__(self, config_file='config.json'):
        self.config_file = config_file
        self.config = self.load_config()
        self.watchers = []  # 配置变更监听器
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return self.validate_config(config)
        except FileNotFoundError:
            logger.info("配置文件不存在，使用默认配置")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"配置文件格式错误: {e}")
            return self.get_default_config()
    
    def validate_config(self, config):
        """验证配置有效性"""
        # 端口范围检查
        port = config.get('proxy', {}).get('port', 8888)
        if not (1024 <= port <= 65535):
            logger.warning(f"端口 {port} 超出范围，使用默认端口 8888")
            config['proxy']['port'] = 8888
        
        # URL格式检查
        helper_url = config.get('mahjong_helper', {}).get('url', '')
        if not helper_url.startswith('http'):
            logger.warning("助手URL格式错误，使用默认URL")
            config['mahjong_helper']['url'] = 'http://localhost:12121/majsoul-client'
        
        return config
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logger.info("配置已保存")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def watch_config(self, callback):
        """监听配置变更"""
        self.watchers.append(callback)
    
    def reload_config(self):
        """重新加载配置"""
        old_config = self.config.copy()
        self.config = self.load_config()
        
        # 通知监听器
        for callback in self.watchers:
            try:
                callback(old_config, self.config)
            except Exception as e:
                logger.error(f"配置变更回调异常: {e}")
```