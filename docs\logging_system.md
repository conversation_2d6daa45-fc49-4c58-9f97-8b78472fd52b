# 日志系统设计

## 日志级别定义

```python
import logging
from logging.handlers import RotatingFileHandler

class ProxyLogger:
    def __init__(self, config):
        self.config = config
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志配置"""
        log_config = self.config.config['logging']
        
        # 创建logger
        logger = logging.getLogger('majsoul_proxy')
        logger.setLevel(getattr(logging, log_config['level'].upper()))
        
        # 文件处理器（带轮转）
        file_handler = RotatingFileHandler(
            log_config['file'],
            maxBytes=self.parse_size(log_config.get('max_size', '10MB')),
            backupCount=log_config.get('backup_count', 5),
            encoding='utf-8'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def parse_size(self, size_str):
        """解析大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
```

## 日志内容设计

### 连接日志
```python
# 连接建立
logger.info(f"[连接{conn_id}] 来自 {client_addr} → {target_host}:{target_port}")

# 数据传输
logger.debug(f"[连接{conn_id}] 转发数据: {len(data)} 字节 ({direction})")

# 连接关闭
logger.info(f"[连接{conn_id}] 连接关闭，持续时间: {duration:.2f}s，传输: {total_bytes} 字节")
```

### 错误日志
```python
# 连接错误
logger.error(f"[连接{conn_id}] 连接目标服务器失败: {host}:{port} - {error}")

# 数据传输错误
logger.warning(f"[连接{conn_id}] 数据传输异常: {error}")

# 助手通信错误
logger.warning(f"助手通信失败: {error}，跳过数据发送")
```

### 性能日志
```python
# 统计信息
logger.info(f"代理统计: 活跃连接 {active_count}，总连接 {total_count}，数据传输 {total_bytes}")

# 性能监控
if response_time > 1.0:
    logger.warning(f"助手响应缓慢: {response_time:.2f}s")
```