from __future__ import annotations
from typing import Dict, List, Tuple
from .point import calc_point_hand
from .agari_rate import calculate_agari_rate_each_tile, calculate_avg_agari_rate


def average_point_and_rate(tiles34: List[int], waits: Dict[int,int], *, is_tsumo: bool, is_parent: bool, round_wind: int, self_wind: int) -> Tuple[float, float]:
    # 简化版：对每个侍牌按剩余张数加权计算点数，和率按剩余张数比
    total_w = sum(waits.values())
    if total_w == 0:
        return 0.0, 0.0
    point_sum = 0.0
    for t, w in waits.items():
        tiles34[t] += 1
        pt = calc_point_hand(tiles34, t, is_tsumo=is_tsumo, is_parent=is_parent, round_wind=round_wind, self_wind=self_wind)
        tiles34[t] -= 1
        point_sum += pt * w
    avg_point = point_sum / total_w
    agari_rate = total_w / 136.0 * 100.0  # 非严格近似
    return avg_point, agari_rate


def calc_avg_point(tiles34: List[int], waits: Dict[int,int], *, discard_tiles: List[int] | None = None, dora_tiles: List[int] | None = None,
                   is_parent: bool, is_riichi: bool, is_tsumo: bool, round_wind: int, self_wind: int) -> Tuple[float, Dict[int, float]]:
    """对齐 Go 的 CalcAvgPoint（简化版）：
    - 使用 calculate_agari_rate_each_tile 计算各侍牌和率
    - 点数使用 calc_point_hand（非振听时荣和点；振听由外部设置 is_tsumo True 切换）
    - 立直时未考虑里宝/一发（后续接入），当前以点数近似
    返回 (平均点数, 各侍牌和率字典)
    """
    rates = calculate_agari_rate_each_tile(waits, discard_tiles, dora_tiles)
    sum_pt = 0.0
    weight = 0.0
    for t, left in waits.items():
        if left <= 0:
            continue
        tiles34[t] += 1
        pt = calc_point_hand(tiles34, t, is_tsumo=is_tsumo, is_parent=is_parent, round_wind=round_wind, self_wind=self_wind)
        tiles34[t] -= 1
        w = rates.get(t, 0.0)
        sum_pt += pt * w
        weight += w
    avg_point = (sum_pt / weight) if weight > 0 else 0.0
    return avg_point, rates


def _fixed_riichi_point(ron_point: float, han: int, is_parent: bool, is_furiten: bool = False) -> float:
    """
    修正立直打点，考虑自摸、里宝和一发的实际打点
    参考Go版本的fixedRiichiPoint算法
    """
    if is_furiten:
        # 振听时只能自摸，点数会有所不同
        return ron_point * 0.8  # 简化处理

    # 亲家点数需要先转换为子家基准
    base_point = ron_point / 1.5 if is_parent else ron_point
    pt = base_point - 100  # 保证亲子落入同一区间

    # 根据点数区间应用不同的修正系数
    if pt <= 1300:
        multiplier = 2730.0 / 1300.0
    elif pt <= 2000:
        multiplier = 3700.0 / 2000.0
    elif pt <= 2600:
        multiplier = 4900.0 / 2600.0
    elif pt <= 3900:
        multiplier = 6300.0 / 3900.0
    elif pt <= 5200:
        multiplier = 7500.0 / 5200.0
    elif pt <= 7700:
        multiplier = 9100.0 / 7700.0
    elif pt <= 8000:
        if han == 4:
            multiplier = 9130.0 / 8000.0
        elif han == 5:
            multiplier = 11200.0 / 8000.0
        else:
            multiplier = 1.0
    elif pt <= 12000:
        if han == 6:
            multiplier = 13030.0 / 12000.0
        elif han == 7:
            multiplier = 15000.0 / 12000.0
        else:
            multiplier = 1.0
    else:
        # 跳满以上暂不修正
        multiplier = 1.0

    return ron_point * multiplier


def calc_avg_riichi_point(tiles34: List[int], waits: Dict[int,int], *, discard_tiles: List[int] | None = None, dora_tiles: List[int] | None = None,
                          is_parent: bool, round_wind: int, self_wind: int, is_furiten: bool = False) -> Tuple[float, Dict[int, float]]:
    """立直时的平均点数（完整版）：
    - 使用 calculate_agari_rate_each_tile 计算各侍牌和率
    - 点数使用 calc_point_hand 并应用立直修正（里宝/一发加成）
    - 考虑振听情况
    返回 (平均点数, 各侍牌和率字典)
    """
    rates = calculate_agari_rate_each_tile(waits, discard_tiles, dora_tiles)
    sum_pt = 0.0
    weight = 0.0

    for t, left in waits.items():
        if left <= 0:
            continue
        tiles34[t] += 1
        # 计算基础点数（荣和）
        base_pt = calc_point_hand(tiles34, t, is_tsumo=False, is_parent=is_parent,
                                  round_wind=round_wind, self_wind=self_wind, is_riichi=True)
        tiles34[t] -= 1

        if base_pt > 0:
            # 应用立直修正
            # 这里需要估算番数，简化处理
            estimated_han = 1  # 立直1番，实际应该更精确计算
            if base_pt >= 2000:
                estimated_han = 2
            if base_pt >= 3900:
                estimated_han = 3
            if base_pt >= 7700:
                estimated_han = 4

            fixed_pt = _fixed_riichi_point(float(base_pt), estimated_han, is_parent, is_furiten)

            w = rates.get(t, 0.0)
            sum_pt += fixed_pt * w
            weight += w

    avg_point = (sum_pt / weight) if weight > 0 else 0.0
    return avg_point, rates