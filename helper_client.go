package main

import (
	"bytes"
	"fmt"
	"net/http"
	"sync"
	"time"
)

// MahjongHelperClient 麻将助手客户端
type MahjongHelperClient struct {
	config     *ProxyConfig
	httpClient *http.Client
	stats      *HelperClientStats
	mutex      sync.RWMutex
}

// HelperClientStats 助手客户端统计信息
type HelperClientStats struct {
	TotalRequests    int64
	SuccessRequests  int64
	FailedRequests   int64
	LastRequestTime  time.Time
	LastSuccessTime  time.Time
	LastErrorTime    time.Time
	LastError        string
	AverageLatency   time.Duration
	IsConnected      bool
}

// NewMahjongHelperClient 创建新的助手客户端
func NewMahjongHelperClient(config *ProxyConfig) *MahjongHelperClient {
	client := &MahjongHelperClient{
		config: config,
		httpClient: &http.Client{
			Timeout: time.Duration(config.MahjongHelper.Timeout * float64(time.Second)),
		},
		stats: &HelperClientStats{
			IsConnected: false,
		},
	}
	
	// 启动连接测试
	go client.startConnectionTest()
	
	return client
}

// SendData 发送数据到麻将助手
func (c *MahjongHelperClient) SendData(data []byte) bool {
	c.mutex.Lock()
	c.stats.TotalRequests++
	c.stats.LastRequestTime = time.Now()
	c.mutex.Unlock()
	
	startTime := time.Now()
	success := false
	
	// 重试机制
	for attempt := 0; attempt <= c.config.MahjongHelper.RetryCount; attempt++ {
		if attempt > 0 {
			// 重试延迟
			time.Sleep(time.Duration(c.config.MahjongHelper.RetryDelay * float64(time.Second)))
		}
		
		if c.sendDataOnce(data) {
			success = true
			break
		}
	}
	
	// 更新统计信息
	c.mutex.Lock()
	latency := time.Since(startTime)
	if success {
		c.stats.SuccessRequests++
		c.stats.LastSuccessTime = time.Now()
		c.stats.IsConnected = true
		
		// 更新平均延迟
		if c.stats.AverageLatency == 0 {
			c.stats.AverageLatency = latency
		} else {
			c.stats.AverageLatency = (c.stats.AverageLatency + latency) / 2
		}
	} else {
		c.stats.FailedRequests++
		c.stats.LastErrorTime = time.Now()
		c.stats.IsConnected = false
	}
	c.mutex.Unlock()
	
	return success
}

// sendDataOnce 单次发送数据
func (c *MahjongHelperClient) sendDataOnce(data []byte) bool {
	url := c.config.GetMahjongHelperURL()
	
	req, err := http.NewRequest("POST", url, bytes.NewReader(data))
	if err != nil {
		c.setLastError(fmt.Sprintf("创建请求失败: %v", err))
		return false
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("User-Agent", "MahjongHelper-Proxy/1.0")
	
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.setLastError(fmt.Sprintf("发送请求失败: %v", err))
		return false
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		c.setLastError(fmt.Sprintf("服务器响应错误: %d %s", resp.StatusCode, resp.Status))
		return false
	}
	
	return true
}

// TestConnection 测试与助手的连接
func (c *MahjongHelperClient) TestConnection() bool {
	url := c.config.GetMahjongHelperURL()
	
	// 发送简单的GET请求测试连接
	testURL := url
	if testURL[len(testURL)-1] != '/' {
		testURL += "/"
	}
	
	req, err := http.NewRequest("GET", testURL, nil)
	if err != nil {
		c.setLastError(fmt.Sprintf("创建测试请求失败: %v", err))
		return false
	}
	
	req.Header.Set("User-Agent", "MahjongHelper-Proxy/1.0")
	
	client := &http.Client{
		Timeout: 5 * time.Second, // 测试连接使用较短超时
	}
	
	resp, err := client.Do(req)
	if err != nil {
		c.setLastError(fmt.Sprintf("连接测试失败: %v", err))
		return false
	}
	defer resp.Body.Close()
	
	// 只要能收到响应就认为连接正常（不管状态码）
	c.mutex.Lock()
	c.stats.IsConnected = true
	c.stats.LastSuccessTime = time.Now()
	c.mutex.Unlock()
	
	return true
}

// startConnectionTest 启动连接测试协程
func (c *MahjongHelperClient) startConnectionTest() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒测试一次连接
	defer ticker.Stop()
	
	// 立即进行一次测试
	c.TestConnection()
	
	for range ticker.C {
		c.TestConnection()
	}
}

// setLastError 设置最后一次错误信息
func (c *MahjongHelperClient) setLastError(error string) {
	c.mutex.Lock()
	c.stats.LastError = error
	c.stats.LastErrorTime = time.Now()
	c.mutex.Unlock()
}

// GetStats 获取统计信息
func (c *MahjongHelperClient) GetStats() *HelperClientStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	// 返回统计信息的副本
	return &HelperClientStats{
		TotalRequests:   c.stats.TotalRequests,
		SuccessRequests: c.stats.SuccessRequests,
		FailedRequests:  c.stats.FailedRequests,
		LastRequestTime: c.stats.LastRequestTime,
		LastSuccessTime: c.stats.LastSuccessTime,
		LastErrorTime:   c.stats.LastErrorTime,
		LastError:       c.stats.LastError,
		AverageLatency:  c.stats.AverageLatency,
		IsConnected:     c.stats.IsConnected,
	}
}

// IsConnected 检查是否连接正常
func (c *MahjongHelperClient) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	return c.stats.IsConnected
}

// GetSuccessRate 获取成功率
func (c *MahjongHelperClient) GetSuccessRate() float64 {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	if c.stats.TotalRequests == 0 {
		return 0.0
	}
	
	return float64(c.stats.SuccessRequests) / float64(c.stats.TotalRequests) * 100.0
}

// UpdateConfig 更新配置
func (c *MahjongHelperClient) UpdateConfig(config *ProxyConfig) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	c.config = config
	c.httpClient.Timeout = time.Duration(config.MahjongHelper.Timeout * float64(time.Second))
}

// Close 关闭客户端
func (c *MahjongHelperClient) Close() {
	// 关闭HTTP客户端的空闲连接
	if transport, ok := c.httpClient.Transport.(*http.Transport); ok {
		transport.CloseIdleConnections()
	}
}

// PrintStats 打印统计信息
func (c *MahjongHelperClient) PrintStats() {
	stats := c.GetStats()
	
	fmt.Println("=== 麻将助手连接统计 ===")
	fmt.Printf("连接状态: %s\n", func() string {
		if stats.IsConnected {
			return "已连接"
		}
		return "未连接"
	}())
	fmt.Printf("总请求数: %d\n", stats.TotalRequests)
	fmt.Printf("成功请求: %d\n", stats.SuccessRequests)
	fmt.Printf("失败请求: %d\n", stats.FailedRequests)
	fmt.Printf("成功率: %.2f%%\n", c.GetSuccessRate())
	
	if stats.AverageLatency > 0 {
		fmt.Printf("平均延迟: %v\n", stats.AverageLatency)
	}
	
	if !stats.LastSuccessTime.IsZero() {
		fmt.Printf("最后成功时间: %s\n", stats.LastSuccessTime.Format("2006-01-02 15:04:05"))
	}
	
	if !stats.LastErrorTime.IsZero() && stats.LastError != "" {
		fmt.Printf("最后错误时间: %s\n", stats.LastErrorTime.Format("2006-01-02 15:04:05"))
		fmt.Printf("最后错误信息: %s\n", stats.LastError)
	}
	
	fmt.Println("========================")
}
