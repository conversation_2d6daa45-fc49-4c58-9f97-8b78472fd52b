package main

import (
	"bufio"
	"fmt"
	"io"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"
)

// MajsoulProxy 代理服务器结构体
type MajsoulProxy struct {
	config           *ProxyConfig
	listener         net.Listener
	helperClient     *MahjongHelperClient
	dataInterceptor  *DataInterceptor
	logger           *ProxyLogger
	errorHandler     *ErrorHandler
	activeConnections map[string]*ProxyConnection
	connectionsMutex sync.RWMutex
	shutdownChan     chan struct{}
	isRunning        bool
}

// ProxyConnection 代理连接信息
type ProxyConnection struct {
	ID           string
	ClientConn   net.Conn
	RemoteConn   net.Conn
	StartTime    time.Time
	BytesRead    int64
	BytesWritten int64
	IsMajsoul    bool
	Host         string
	Port         int
}

// NewMajsoulProxy 创建新的代理服务器实例
func NewMajsoulProxy(config *ProxyConfig) (*MajsoulProxy, error) {
	logger, err := NewProxyLogger(config)
	if err != nil {
		return nil, fmt.Errorf("创建日志器失败: %v", err)
	}

	errorHandler := NewErrorHandler(logger)

	proxy := &MajsoulProxy{
		config:            config,
		helperClient:      NewMahjongHelperClient(config),
		dataInterceptor:   NewDataInterceptor(config),
		logger:            logger,
		errorHandler:      errorHandler,
		activeConnections: make(map[string]*ProxyConnection),
		shutdownChan:      make(chan struct{}),
		isRunning:         false,
	}

	return proxy, nil
}

// Start 启动代理服务器
func (p *MajsoulProxy) Start() error {
	addr := fmt.Sprintf("%s:%d", p.config.Proxy.Host, p.config.Proxy.Port)

	listener, err := net.Listen("tcp", addr)
	if err != nil {
		p.logger.Error("启动代理服务器失败: %v", err)
		return fmt.Errorf("启动代理服务器失败: %v", err)
	}

	p.listener = listener
	p.isRunning = true

	p.logger.Info("代理服务器已启动，监听地址: %s", addr)

	// 启动连接处理循环
	go p.acceptConnections()

	return nil
}

// Stop 停止代理服务器
func (p *MajsoulProxy) Stop() error {
	if !p.isRunning {
		return nil
	}

	p.logger.Info("正在停止代理服务器...")

	p.isRunning = false
	close(p.shutdownChan)

	// 关闭监听器
	if p.listener != nil {
		p.listener.Close()
	}

	// 关闭所有活动连接
	p.connectionsMutex.Lock()
	connectionCount := len(p.activeConnections)
	for _, conn := range p.activeConnections {
		conn.ClientConn.Close()
		if conn.RemoteConn != nil {
			conn.RemoteConn.Close()
		}
	}
	p.connectionsMutex.Unlock()

	// 关闭助手客户端
	if p.helperClient != nil {
		p.helperClient.Close()
	}

	// 关闭日志器
	if p.logger != nil {
		p.logger.Close()
	}

	p.logger.Info("代理服务器已停止，关闭了 %d 个连接", connectionCount)
	return nil
}

// acceptConnections 接受客户端连接
func (p *MajsoulProxy) acceptConnections() {
	for p.isRunning {
		clientConn, err := p.listener.Accept()
		if err != nil {
			if p.isRunning {
				p.logger.Error("接受连接失败: %v", err)
			}
			continue
		}

		p.logger.Debug("接受新连接: %s", clientConn.RemoteAddr().String())

		// 为每个连接启动处理协程
		go p.handleConnection(clientConn)
	}
}

// handleConnection 处理单个客户端连接
func (p *MajsoulProxy) handleConnection(clientConn net.Conn) {
	defer clientConn.Close()
	
	// 设置连接超时
	clientConn.SetReadDeadline(time.Now().Add(time.Duration(p.config.Proxy.ConnectionTimeout) * time.Second))
	
	// 读取CONNECT请求
	host, port, err := p.parseConnectRequest(clientConn)
	if err != nil {
		p.logger.Debug("解析CONNECT请求失败: %v", err)
		p.sendErrorResponse(clientConn, "400 Bad Request")
		return
	}

	// 检查是否为雀魂流量
	isMajsoul := p.isMajsoulHost(host)

	// 建立与目标服务器的连接
	remoteConn, err := p.connectToRemote(host, port)
	if err != nil {
		p.errorHandler.HandleConnectionError(err, host, port)
		if strings.Contains(err.Error(), "timeout") {
			p.sendErrorResponse(clientConn, "502 Bad Gateway")
		} else {
			p.sendErrorResponse(clientConn, "503 Service Unavailable")
		}
		return
	}
	defer remoteConn.Close()
	
	// 发送连接成功响应
	_, err = clientConn.Write([]byte("HTTP/1.1 200 Connection established\r\n\r\n"))
	if err != nil {
		p.logger.Error("发送连接响应失败: %v", err)
		return
	}
	
	// 创建连接记录
	connID := fmt.Sprintf("%s-%d", clientConn.RemoteAddr().String(), time.Now().UnixNano())
	proxyConn := &ProxyConnection{
		ID:         connID,
		ClientConn: clientConn,
		RemoteConn: remoteConn,
		StartTime:  time.Now(),
		IsMajsoul:  isMajsoul,
		Host:       host,
		Port:       port,
	}
	
	// 注册连接
	p.connectionsMutex.Lock()
	p.activeConnections[connID] = proxyConn
	p.connectionsMutex.Unlock()
	
	// 清理连接记录
	defer func() {
		p.connectionsMutex.Lock()
		delete(p.activeConnections, connID)
		p.connectionsMutex.Unlock()
	}()
	
	if isMajsoul {
		p.logger.Info("检测到雀魂连接: %s:%d", host, port)
	} else {
		p.logger.Debug("普通连接: %s:%d", host, port)
	}
	
	// 启动双向数据转发
	p.startForwarding(proxyConn)
}

// parseConnectRequest 解析CONNECT请求
func (p *MajsoulProxy) parseConnectRequest(conn net.Conn) (string, int, error) {
	reader := bufio.NewReader(conn)
	
	// 读取第一行
	line, err := reader.ReadLine()
	if err != nil {
		return "", 0, fmt.Errorf("读取请求行失败: %v", err)
	}
	
	requestLine := string(line)
	if !strings.HasPrefix(requestLine, "CONNECT ") {
		return "", 0, fmt.Errorf("非CONNECT请求: %s", requestLine)
	}
	
	// 解析目标地址
	parts := strings.Split(requestLine, " ")
	if len(parts) < 2 {
		return "", 0, fmt.Errorf("CONNECT请求格式错误: %s", requestLine)
	}
	
	target := parts[1]
	if !strings.Contains(target, ":") {
		return "", 0, fmt.Errorf("目标地址格式错误: %s", target)
	}
	
	// 分离主机名和端口
	lastColon := strings.LastIndex(target, ":")
	host := target[:lastColon]
	portStr := target[lastColon+1:]
	
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return "", 0, fmt.Errorf("端口格式错误: %s", portStr)
	}
	
	// 读取剩余的请求头（直到空行）
	for {
		line, err := reader.ReadLine()
		if err != nil {
			break
		}
		if len(line) == 0 {
			break
		}
	}
	
	return host, port, nil
}

// connectToRemote 连接到目标服务器
func (p *MajsoulProxy) connectToRemote(host string, port int) (net.Conn, error) {
	addr := fmt.Sprintf("%s:%d", host, port)
	timeout := time.Duration(p.config.Proxy.ConnectionTimeout) * time.Second
	
	conn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		return nil, err
	}
	
	return conn, nil
}

// sendErrorResponse 发送错误响应
func (p *MajsoulProxy) sendErrorResponse(conn net.Conn, status string) {
	response := fmt.Sprintf("HTTP/1.1 %s\r\n\r\n", status)
	conn.Write([]byte(response))
}

// isMajsoulHost 检查是否为雀魂主机
func (p *MajsoulProxy) isMajsoulHost(host string) bool {
	for _, majsoulHost := range p.config.Majsoul.Hosts {
		if strings.Contains(host, majsoulHost) {
			return true
		}
	}
	return false
}

// startForwarding 启动双向数据转发
func (p *MajsoulProxy) startForwarding(proxyConn *ProxyConnection) {
	var wg sync.WaitGroup
	wg.Add(2)
	
	// 客户端 → 服务器
	go func() {
		defer wg.Done()
		p.forwardData(proxyConn.ClientConn, proxyConn.RemoteConn, false, proxyConn)
	}()
	
	// 服务器 → 客户端
	go func() {
		defer wg.Done()
		p.forwardData(proxyConn.RemoteConn, proxyConn.ClientConn, proxyConn.IsMajsoul, proxyConn)
	}()
	
	wg.Wait()
}

// forwardData 转发数据
func (p *MajsoulProxy) forwardData(src, dst net.Conn, interceptData bool, proxyConn *ProxyConnection) {
	buffer := make([]byte, p.config.Advanced.BufferSize)

	defer func() {
		// 确保连接关闭
		src.Close()
		dst.Close()
	}()

	for {
		n, err := src.Read(buffer)
		if err != nil {
			if err != io.EOF {
				p.errorHandler.HandleDataTransferError(err, proxyConn.ID)
			}
			break
		}

		data := buffer[:n]

		// 雀魂数据拦截
		if interceptData && proxyConn.IsMajsoul {
			p.interceptMajsoulData(data)
		}

		// 转发数据
		_, err = dst.Write(data)
		if err != nil {
			p.errorHandler.HandleDataTransferError(err, proxyConn.ID)
			break
		}

		// 更新统计信息
		if src == proxyConn.ClientConn {
			proxyConn.BytesRead += int64(n)
		} else {
			proxyConn.BytesWritten += int64(n)
		}
	}
}

// interceptMajsoulData 拦截雀魂数据
func (p *MajsoulProxy) interceptMajsoulData(data []byte) {
	// 使用数据拦截器分析数据
	gameData := p.dataInterceptor.InterceptData(data)
	if gameData == nil {
		return
	}

	// 发送到助手
	go p.helperClient.SendData(gameData)
}

// GetConnectionStats 获取连接统计信息
func (p *MajsoulProxy) GetConnectionStats() map[string]*ProxyConnection {
	p.connectionsMutex.RLock()
	defer p.connectionsMutex.RUnlock()
	
	stats := make(map[string]*ProxyConnection)
	for id, conn := range p.activeConnections {
		stats[id] = conn
	}
	
	return stats
}
