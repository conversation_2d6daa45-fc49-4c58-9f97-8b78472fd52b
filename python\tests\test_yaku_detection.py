"""
役种判定测试 - 对照Go版本的yaku_test.go
"""
import unittest
from mahjong_py import str_to_tiles34, calc_point_hand
from mahjong_py.yaku import yaku_normal, HandInfo
from mahjong_py.yakuman import yakuman_list
from mahjong_py.divide import divide_tiles34
from mahjong_py.config import set_consider_old_yaku
from mahjong_py.yaku_data import *


class TestYakuDetection(unittest.TestCase):
    """役种判定测试类"""
    
    def setUp(self):
        """测试前设置"""
        set_consider_old_yaku(False)  # 默认关闭老役
    
    def _get_yaku_names(self, hand_str: str, win_tile_str: str, is_tsumo: bool = False) -> list:
        """获取手牌的役种名称列表"""
        tiles34, _ = str_to_tiles34(hand_str)
        win_tile34, _ = str_to_tiles34(win_tile_str)
        win_tile = next(i for i, c in enumerate(win_tile34) if c > 0)
        
        divide_results = divide_tiles34(tiles34)
        if not divide_results:
            return []
        
        all_yaku = []
        for divide_result in divide_results:
            hi = HandInfo(
                tiles34=tiles34,
                divide=divide_result,
                melds=[],
                win_tile=win_tile,
                is_tsumo=is_tsumo,
                is_riichi=False,
                is_daburii=False
            )
            hi.round_wind = 27  # 东风
            hi.self_wind = 27   # 自风东
            
            yaku = yaku_normal(hi)
            yakuman = yakuman_list(
                tiles34, win_tile,
                divide_result.pair_tile,
                divide_result.shuntsu_first_tiles,
                divide_result.kotsu_tiles,
                divide_result.is_chiitoi
            )
            
            all_yaku.extend(yaku + yakuman)
        
        return sorted(list(set(all_yaku)))
    
    def test_basic_yaku_detection(self):
        """测试基础役种判定 - 对照Go版本Test_findYakuTypes"""
        test_cases = [
            # (手牌, 和牌, 是否自摸, 期望包含的役种, 描述)
            ("99s 112233445566z", "9s", False, [YakuChiitoi, YakuHonroutou, YakuHonitsu], "七对 混老头 混一色"),
            ("22m 112233445566z", "2m", False, [YakuChiitoi, YakuHonitsu], "七对 混一色"),
            ("345m 345s 334455p 44z", "3m", False, [YakuPinfu, YakuIipeikou, YakuSanshokuDoujun], "平和 一杯口 三色"),
            ("333m 333s 333345p 11z", "3m", False, [YakuSanshokuDoukou], "三色同刻"),
            ("234m 333p 55666777z", "3m", False, [YakuSanAnkou, YakuYakuhai, YakuShousangen], "三暗刻 役牌 小三元"),
            ("123445566789m 11z", "3m", False, [YakuIipeikou, YakuIttsuu, YakuHonitsu], "一杯口 一通 混一色"),
            ("123m 123999s 11155z", "3m", False, [YakuYakuhai, YakuChanta], "役牌 混全"),
            ("334455m 667788s 77z", "3m", False, [YakuRyanpeikou], "两杯口"),
            ("334455m 667788s 44z", "3m", False, [YakuPinfu, YakuRyanpeikou], "平和 两杯口"),
            ("123m 123999s 11789p", "3m", False, [YakuJunchan], "纯全"),
        ]
        
        for hand_str, win_tile_str, is_tsumo, expected_yaku, description in test_cases:
            with self.subTest(hand=hand_str, win=win_tile_str, desc=description):
                yaku_list = self._get_yaku_names(hand_str, win_tile_str, is_tsumo)
                
                # 检查期望的役种是否都存在
                for expected in expected_yaku:
                    self.assertIn(expected, yaku_list, 
                                f"Missing {expected} in {description}. Got: {yaku_list}")
    
    def test_yakuman_detection(self):
        """测试役满判定"""
        yakuman_cases = [
            ("11122345678999m", "3m", False, [YakuChuurenpooto], "九莲宝灯"),
            ("11122345678999m", "2m", True, [YakuSuuAnkou], "四暗刻"),
            ("11122233344455z", "5z", False, [YakuTsuuiisou, YakuDaisangen, YakuDaisuushii], "字一色+大三元+大四喜"),
            ("111222333444m 11z", "3m", True, [YakuSuuAnkou], "四暗刻（自摸）"),
        ]
        
        for hand_str, win_tile_str, is_tsumo, expected_yakuman, description in yakuman_cases:
            with self.subTest(hand=hand_str, win=win_tile_str, desc=description):
                yaku_list = self._get_yaku_names(hand_str, win_tile_str, is_tsumo)
                
                # 检查期望的役满是否存在
                for expected in expected_yakuman:
                    self.assertIn(expected, yaku_list,
                                f"Missing {expected} in {description}. Got: {yaku_list}")
    
    def test_old_yaku_detection(self):
        """测试老役判定"""
        set_consider_old_yaku(True)
        
        old_yaku_cases = [
            ("22334455667788m", "2m", False, [YakuDaisuurin], "大数邻"),
            ("22334455667788p", "2p", False, [YakuDaisharin], "大车轮"),
            ("22334455667788s", "2s", False, [YakuDaichikurin], "大竹林"),
            ("11223344556677z", "2z", False, [YakuDaichisei], "大七星"),
            ("123m 456p 789s 111z 22z", "2z", False, [YakuUumensai], "五门齐"),
            ("111m 222m 333m 456p 77z", "7z", False, [YakuSanrenkou], "三连刻"),
            ("123m 123m 123m 456p 77z", "7z", False, [YakuIsshokusanjun], "一色三顺"),
        ]
        
        for hand_str, win_tile_str, is_tsumo, expected_yaku, description in old_yaku_cases:
            with self.subTest(hand=hand_str, win=win_tile_str, desc=description):
                yaku_list = self._get_yaku_names(hand_str, win_tile_str, is_tsumo)
                
                # 检查期望的老役是否存在
                for expected in expected_yaku:
                    self.assertIn(expected, yaku_list,
                                f"Missing {expected} in {description}. Got: {yaku_list}")
        
        set_consider_old_yaku(False)  # 恢复默认设置
    
    def test_tsumo_yaku(self):
        """测试自摸相关役种"""
        # 自摸时应该有自摸役
        yaku_list = self._get_yaku_names("345m 345s 334455p 44z", "3m", is_tsumo=True)
        # 注意：这里需要根据实际实现调整，可能需要在HandInfo中正确设置is_tsumo
        
        # 四暗刻只有自摸时才成立
        yaku_list_tsumo = self._get_yaku_names("111222333444m 11z", "3m", is_tsumo=True)
        yaku_list_ron = self._get_yaku_names("111222333444m 11z", "3m", is_tsumo=False)
        
        # 自摸时应该有四暗刻
        self.assertIn(YakuSuuAnkou, yaku_list_tsumo, "四暗刻应该在自摸时成立")
        # 荣和时不应该有四暗刻（除非是单骑）
        if YakuSuuAnkou in yaku_list_ron:
            # 如果荣和也有四暗刻，说明是单骑四暗刻
            pass
    
    def test_riichi_yaku(self):
        """测试立直相关役种"""
        tiles34, _ = str_to_tiles34("345m 222789p 333s 66z")
        win_tile34, _ = str_to_tiles34("3m")
        win_tile = next(i for i, c in enumerate(win_tile34) if c > 0)
        
        divide_results = divide_tiles34(tiles34)
        if divide_results:
            hi = HandInfo(
                tiles34=tiles34,
                divide=divide_results[0],
                melds=[],
                win_tile=win_tile,
                is_tsumo=False,
                is_riichi=True,  # 设置立直
                is_daburii=False
            )
            hi.round_wind = 28  # 南风
            hi.self_wind = 30   # 自风西
            
            yaku = yaku_normal(hi)
            self.assertIn(YakuRiichi, yaku, "应该检测到立直役")
    
    def test_wind_yaku(self):
        """测试风牌役种"""
        # 场风役牌
        tiles34, _ = str_to_tiles34("345m 222789p 333s 111z")  # 111z = 东东东
        win_tile34, _ = str_to_tiles34("3m")
        win_tile = next(i for i, c in enumerate(win_tile34) if c > 0)
        
        divide_results = divide_tiles34(tiles34)
        if divide_results:
            hi = HandInfo(
                tiles34=tiles34,
                divide=divide_results[0],
                melds=[],
                win_tile=win_tile,
                is_tsumo=False,
                is_riichi=False,
                is_daburii=False
            )
            hi.round_wind = 27  # 东风场
            hi.self_wind = 28   # 自风南
            
            yaku = yaku_normal(hi)
            # 东风在东风场是场风役牌
            self.assertIn(YakuYakuhai, yaku, "应该检测到场风役牌")


if __name__ == '__main__':
    unittest.main()
