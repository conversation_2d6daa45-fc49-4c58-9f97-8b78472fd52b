from __future__ import annotations
from dataclasses import dataclass
from typing import List, Dict, Tuple

from .shanten import calculate_shanten

ALL_TILES = list(range(34))


@dataclass
class Waits:
    counts: Dict[int, int]

    def all_count(self) -> int:
        return sum(self.counts.values())


@dataclass
class Result13:
    tiles34: List[int]
    shanten: int
    waits: Waits
    next_shanten_waits_count_map: Dict[int, int]
    avg_next_shanten_waits_count: float
    improves: Dict[int, int]
    avg_improve_waits_count: float
    mixed_waits_score: float


@dataclass
class Result14:
    discard_tile: int
    result13: Result13


def _count_left_simple(tiles34: List[int]) -> List[int]:
    return [max(0, 4 - c) for c in tiles34]


def _calc_waits_only(tiles34: List[int]) -> Waits:
    base = calculate_shanten(tiles34)
    left = _count_left_simple(tiles34)
    waits: Dict[int, int] = {}
    for t in ALL_TILES:
        if left[t] == 0:
            continue
        tiles34[t] += 1
        if calculate_shanten(tiles34) < base:
            waits[t] = left[t]
        tiles34[t] -= 1
    return Waits(waits)


def _calc_waits_and_speed(tiles34: List[int]) -> Tuple[Waits, Dict[int, int], float]:
    base = calculate_shanten(tiles34)
    left = _count_left_simple(tiles34)
    waits: Dict[int, int] = {}
    next_map: Dict[int, int] = {}
    w_sum = 0
    w_weight = 0
    for t in ALL_TILES:
        if left[t] == 0:
            continue
        tiles34[t] += 1  # draw -> 14
        s = calculate_shanten(tiles34)
        if s < base:
            waits[t] = left[t]
            # 从14张选择最佳切牌回到13张，且保持向听不变（即 s13 == s），并计算该13张的进张数（最大）
            best_next = 0
            for d in ALL_TILES:
                if tiles34[d] == 0:
                    continue
                tiles34[d] -= 1  # back to 13
                s13 = calculate_shanten(tiles34)
                if s13 == s:
                    w2 = _calc_waits_only(tiles34)
                    best_next = max(best_next, w2.all_count())
                tiles34[d] += 1
            next_map[t] = best_next
            w_sum += left[t] * best_next
            w_weight += left[t]
        tiles34[t] -= 1
    avg_next = (w_sum / w_weight) if w_weight > 0 else 0.0
    return Waits(waits), next_map, avg_next


def _calc_improves(tiles34: List[int], base_shanten: int, base_waits_total: int) -> Tuple[Dict[int, int], float]:
    left = _count_left_simple(tiles34)
    improves: Dict[int, int] = {}
    imp_sum = 0
    weight = 0
    for t in ALL_TILES:
        if left[t] == 0:
            continue
        tiles34[t] += 1  # draw -> 14
        best_waits_total = base_waits_total
        for d in ALL_TILES:
            if tiles34[d] == 0:
                continue
            tiles34[d] -= 1  # back to 13
            s13 = calculate_shanten(tiles34)
            if s13 == base_shanten:
                w2, _, _ = _calc_waits_and_speed(tiles34)
                best_waits_total = max(best_waits_total, w2.all_count())
            tiles34[d] += 1
        inc = best_waits_total - base_waits_total
        if inc > 0:
            improves[t] = inc
        imp_sum += left[t] * best_waits_total
        weight += left[t]
        tiles34[t] -= 1
    avg_improve = (imp_sum / weight) if weight > 0 else float(base_waits_total)
    return improves, avg_improve


def analyze_13(tiles34: List[int]) -> Result13:
    s = calculate_shanten(tiles34)
    waits, next_map, avg_next = _calc_waits_and_speed(tiles34[:])
    base_waits_total = waits.all_count()
    improves, avg_imp = _calc_improves(tiles34[:], s, base_waits_total)
    mixed = base_waits_total + 0.5 * avg_next + 0.3 * avg_imp
    return Result13(tiles34[:], s, waits, next_map, avg_next, improves, avg_imp, mixed)


def best_discards_with_improves(tiles34: List[int]) -> Tuple[int, List[Result14]]:
    count = sum(tiles34)
    if count % 3 != 2:
        raise ValueError("need 14 tiles to analyze discards")
    min_s = 99
    results: List[Result14] = []
    for d in ALL_TILES:
        if tiles34[d] == 0:
            continue
        tiles34[d] -= 1
        r13 = analyze_13(tiles34)
        if r13.shanten < min_s:
            min_s = r13.shanten
            results.clear()
        if r13.shanten == min_s:
            results.append(Result14(discard_tile=d, result13=r13))
        tiles34[d] += 1
    results.sort(key=lambda r: (r.result13.mixed_waits_score, r.result13.waits.all_count()), reverse=True)
    return min_s, results 