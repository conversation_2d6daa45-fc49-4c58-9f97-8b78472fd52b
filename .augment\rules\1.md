---
type: "manual"
---

# Cursor 规则: Go 语言转 Python 语言设计指南

## 核心原则

1. **保持功能完整性**：转换过程必须保持原始 Go 代码的所有功能，禁止简化或省略任何功能点。
2. **尊重语言特性**：利用 Python 的特性重新实现 Go 的功能，而非生硬翻译语法。
3. **高效测试策略**：保持测试文件的精简和有效性，避免创建过多的测试文件。

## Go 到 Python 转换规则

### 1. 数据类型转换

- Go 的基本类型 (`int`, `string`, `bool` 等) 映射到 Python 对应类型
- Go 的 `struct` 转换为 Python `class`
- Go 的 `slice` 和 `array` 转换为 Python `list`
- Go 的 `map` 转换为 Python `dict`
- Go 的指针处理转换为 Python 的引用传递或适当的替代方案

### 2. 错误处理

- Go 的 `if err != nil` 模式转换为 Python 的异常处理机制
- 实现适当的自定义异常类替代 Go 的错误类型
- 保留原始错误信息和处理逻辑

### 3. 并发处理

- Go 的 goroutine 转换为 Python 的 `threading` 或 `asyncio`
- Go 的 channel 使用 Python 的 `queue.Queue` 或 `asyncio.Queue` 实现
- 确保并发模型的正确转换，保持原有的并发控制逻辑

### 4. 包结构转换

- 保持模块的逻辑组织，将 Go 包转换为 Python 模块
- 遵循 Python 的导入和命名约定
- 将 Go 的公共/私有约定 (大小写首字母) 转换为 Python 的约定 (下划线前缀)

## 开发和调试规则

1. **禁止简化功能**
   - 必须完整实现所有原始功能，即使实现复杂
   - 保持原始代码的业务逻辑和边界条件处理
   - 禁止跳过复杂的逻辑部分或留下 TODO 注释

2. **测试文件管理**
   - 每个模块使用单一测试文件，避免过度分割测试
   - 禁止为每个小功能创建单独的测试文件
   - 使用参数化测试减少测试代码重复
   - 测试文件应与源代码结构对应，保持清晰的组织

3. **代码质量控制**
   - 遵循 Python 风格指南 (PEP 8)
   - 添加适当的类型注解，尤其是对应 Go 的静态类型
   - 保持代码的可读性和可维护性
   - 添加必要的文档字符串，解释转换的逻辑

## 转换过程管理

1. 分阶段进行转换，优先核心功能
2. 建立功能对照表，确保所有功能都被正确转换
3. 对每个转换的模块进行单元测试，确保功能等同
4. 进行集成测试，验证模块间交互正确性

遵循这些规则将确保 Go 到 Python 的转换过程既保持了原始代码的完整功能，又符合 Python 的最佳实践，同时避免了过度简化和测试文件泛滥的问题。