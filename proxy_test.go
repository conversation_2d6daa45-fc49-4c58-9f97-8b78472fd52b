package main

import (
	"bytes"
	"net"
	"net/http"
	"testing"
	"time"
)

// TestProxyConfig 测试配置管理
func TestProxyConfig(t *testing.T) {
	// 创建临时配置文件
	configFile := "test_config.json"
	defer func() {
		// 清理测试文件
		// os.Remove(configFile)
	}()
	
	config := NewProxyConfig(configFile)
	
	// 测试默认配置
	if config.Proxy.Port != 8888 {
		t.<PERSON><PERSON><PERSON>("期望默认端口为 8888，实际为 %d", config.Proxy.Port)
	}
	
	if config.Proxy.Host != "127.0.0.1" {
		t.<PERSON>rrorf("期望默认主机为 127.0.0.1，实际为 %s", config.Proxy.Host)
	}
	
	// 测试雀魂主机识别
	if !config.IsMajsoulHost("majsoul.com") {
		t.Error("应该识别 majsoul.com 为雀魂主机")
	}
	
	if config.IsMajsoulHost("google.com") {
		t.Error("不应该识别 google.com 为雀魂主机")
	}
}

// TestDataInterceptor 测试数据拦截器
func TestDataInterceptor(t *testing.T) {
	config := NewProxyConfig("test_config.json")
	interceptor := NewDataInterceptor(config)
	
	// 测试JSON数据识别
	jsonData := []byte(`{"account_id": 12345, "tiles": [1, 2, 3]}`)
	if !interceptor.isJSONData(jsonData) {
		t.Error("应该识别为JSON数据")
	}
	
	// 测试非JSON数据
	binaryData := []byte{0x00, 0x01, 0x02, 0x03}
	if interceptor.isJSONData(binaryData) {
		t.Error("不应该识别为JSON数据")
	}
	
	// 测试WebSocket帧识别
	// 构造一个简单的WebSocket文本帧
	wsFrame := []byte{0x81, 0x05, 'h', 'e', 'l', 'l', 'o'}
	if !interceptor.isWebSocketFrame(wsFrame) {
		t.Error("应该识别为WebSocket帧")
	}
	
	// 测试数据类型获取
	dataType := interceptor.GetDataType(jsonData)
	if dataType != "JSON" {
		t.Errorf("期望数据类型为 JSON，实际为 %s", dataType)
	}
}

// TestMahjongHelperClient 测试助手客户端
func TestMahjongHelperClient(t *testing.T) {
	config := NewProxyConfig("test_config.json")
	
	// 修改配置为测试URL
	config.MahjongHelper.URL = "http://httpbin.org/post"
	config.MahjongHelper.Timeout = 5.0
	
	client := NewMahjongHelperClient(config)
	defer client.Close()
	
	// 测试发送数据
	testData := []byte("test data")
	success := client.SendData(testData)
	
	// 由于httpbin.org可能不可用，这里只检查方法是否正常执行
	t.Logf("发送数据结果: %t", success)
	
	// 测试统计信息
	stats := client.GetStats()
	if stats.TotalRequests == 0 {
		t.Error("应该有请求记录")
	}
}

// TestProxyServer 测试代理服务器基本功能
func TestProxyServer(t *testing.T) {
	config := NewProxyConfig("test_config.json")
	config.Proxy.Port = 18888 // 使用不同的端口避免冲突
	
	proxy, err := NewMajsoulProxy(config)
	if err != nil {
		t.Fatalf("创建代理服务器失败: %v", err)
	}
	
	// 启动代理服务器
	err = proxy.Start()
	if err != nil {
		t.Fatalf("启动代理服务器失败: %v", err)
	}
	defer proxy.Stop()
	
	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)
	
	// 测试连接
	conn, err := net.Dial("tcp", "127.0.0.1:18888")
	if err != nil {
		t.Fatalf("连接代理服务器失败: %v", err)
	}
	defer conn.Close()
	
	// 发送CONNECT请求
	connectRequest := "CONNECT httpbin.org:80 HTTP/1.1\r\nHost: httpbin.org:80\r\n\r\n"
	_, err = conn.Write([]byte(connectRequest))
	if err != nil {
		t.Fatalf("发送CONNECT请求失败: %v", err)
	}
	
	// 读取响应
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		t.Fatalf("读取响应失败: %v", err)
	}
	
	response := string(buffer[:n])
	if !bytes.Contains([]byte(response), []byte("200 Connection established")) {
		t.Errorf("期望收到连接建立响应，实际收到: %s", response)
	}
}

// TestCONNECTRequestParsing 测试CONNECT请求解析
func TestCONNECTRequestParsing(t *testing.T) {
	config := NewProxyConfig("test_config.json")
	proxy, _ := NewMajsoulProxy(config)
	
	// 模拟CONNECT请求
	connectData := "CONNECT majsoul.com:443 HTTP/1.1\r\nHost: majsoul.com:443\r\n\r\n"
	
	// 创建模拟连接
	server, client := net.Pipe()
	defer server.Close()
	defer client.Close()
	
	// 在另一个goroutine中写入数据
	go func() {
		client.Write([]byte(connectData))
		client.Close()
	}()
	
	// 解析请求
	host, port, err := proxy.parseConnectRequest(server)
	if err != nil {
		t.Fatalf("解析CONNECT请求失败: %v", err)
	}
	
	if host != "majsoul.com" {
		t.Errorf("期望主机为 majsoul.com，实际为 %s", host)
	}
	
	if port != 443 {
		t.Errorf("期望端口为 443，实际为 %d", port)
	}
	
	// 测试雀魂主机识别
	if !proxy.isMajsoulHost(host) {
		t.Error("应该识别为雀魂主机")
	}
}

// TestWebSocketPayloadExtraction 测试WebSocket载荷提取
func TestWebSocketPayloadExtraction(t *testing.T) {
	config := NewProxyConfig("test_config.json")
	interceptor := NewDataInterceptor(config)
	
	// 构造WebSocket文本帧（未掩码）
	payload := "Hello, World!"
	frame := make([]byte, 2+len(payload))
	frame[0] = 0x81 // FIN=1, opcode=1 (text)
	frame[1] = byte(len(payload)) // payload length
	copy(frame[2:], payload)
	
	extracted := interceptor.extractWebSocketPayload(frame)
	if extracted == nil {
		t.Fatal("提取WebSocket载荷失败")
	}
	
	if string(extracted) != payload {
		t.Errorf("期望载荷为 '%s'，实际为 '%s'", payload, string(extracted))
	}
}

// TestConfigValidation 测试配置验证
func TestConfigValidation(t *testing.T) {
	config := NewProxyConfig("test_config.json")
	
	// 测试无效端口
	config.Proxy.Port = 99999
	config.validateConfig()
	
	if config.Proxy.Port != 8888 {
		t.Error("无效端口应该被重置为默认值")
	}
	
	// 测试无效URL
	config.MahjongHelper.URL = "invalid-url"
	config.validateConfig()
	
	if config.MahjongHelper.URL == "invalid-url" {
		t.Error("无效URL应该被重置为默认值")
	}
}

// BenchmarkDataInterception 性能测试：数据拦截
func BenchmarkDataInterception(b *testing.B) {
	config := NewProxyConfig("test_config.json")
	interceptor := NewDataInterceptor(config)
	
	testData := []byte(`{"account_id": 12345, "tiles": [1, 2, 3, 4, 5]}`)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		interceptor.InterceptData(testData)
	}
}

// BenchmarkJSONParsing 性能测试：JSON解析
func BenchmarkJSONParsing(b *testing.B) {
	config := NewProxyConfig("test_config.json")
	interceptor := NewDataInterceptor(config)
	
	testData := []byte(`{"account_id": 12345, "tiles": [1, 2, 3, 4, 5], "dora": [6, 7], "seat": 0}`)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		interceptor.isJSONData(testData)
	}
}

// TestIntegration 集成测试
func TestIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}
	
	// 这里可以添加更复杂的集成测试
	// 例如启动完整的代理服务器，模拟客户端连接等
	t.Log("集成测试通过")
}
