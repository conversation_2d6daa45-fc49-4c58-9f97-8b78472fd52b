from __future__ import annotations
from dataclasses import dataclass
from typing import List, Sequence

# 参考 Go 版思路的可运行近似实现

# 基础放铳率（示意值，可后续调整/拟合）
# 顺序：无筋、单筋、半筋、双筋
BASE_SUJI_RISK_BY_TURN = [
    # turn index -> risk%
    # 早巡高、晚巡低（示意）
    [20.0, 18.0, 16.0, 14.0, 12.0, 11.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.8, 6.6, 6.4, 6.2, 6.0, 5.8, 5.6],  # 无筋
    [14.0, 13.0, 12.0, 11.0, 10.5, 10.0, 9.5, 9.0, 8.8, 8.5, 8.2, 8.0, 7.8, 7.6, 7.4, 7.2, 7.0, 6.8, 6.6, 6.4],   # 单筋
    [10.0, 9.6, 9.2, 8.8, 8.5, 8.2, 8.0, 7.8, 7.6, 7.4, 7.2, 7.0, 6.8, 6.6, 6.4, 6.2, 6.0, 5.8, 5.6, 5.4],      # 半筋
    [7.0, 6.8, 6.6, 6.4, 6.2, 6.0, 5.8, 5.6, 5.4, 5.2, 5.0, 4.8, 4.6, 4.4, 4.2, 4.0, 3.8, 3.6, 3.5, 3.4],         # 双筋
]

DORA_RISK_MULTI = {
    # tileType: multiplier
    'none': 1.0,
    'near1': 1.15,
    'near2': 1.05,
    'dora': 1.3,
}

MAX_TURN_INDEX = len(BASE_SUJI_RISK_BY_TURN[0]) - 1


def _clip_turn(t: int) -> int:
    if t < 0:
        return 0
    if t > MAX_TURN_INDEX:
        return MAX_TURN_INDEX
    return t


def _suji_type_for_idx(idx: int, safeTiles34: Sequence[bool], leftTiles34: Sequence[int]) -> int:
    # 返回 0=无筋,1=单筋,2=半筋,3=双筋 的简化判断
    # 参照 Go 版：根据 “现物/NC” 推断两面可能性减少
    i = idx % 9
    base = idx - i
    def is_safe(x: int) -> bool:
        return safeTiles34[x]
    # 无/单/半/双筋判断（简化）：
    # 左右3距离是否有安全/NC（left==0视作通过）
    left3 = base + max(0, i - 3)
    right3 = base + min(8, i + 3)
    # 单筋条件：存在一侧被“通过”（现物或NC）
    single = (i >= 3 and (is_safe(idx - 3) or leftTiles34[idx - 1] == 0)) or (i <= 5 and (is_safe(idx + 3) or leftTiles34[idx + 1] == 0))
    # 双筋条件：两侧都“通过”
    double = (i >= 3 and (is_safe(idx - 3) or leftTiles34[idx - 1] == 0)) and (i <= 5 and (is_safe(idx + 3) or leftTiles34[idx + 1] == 0))
    # 半筋：中张部分单侧“通过”或边张一侧通过
    half = single and not double
    if double:
        return 3
    if half:
        return 2
    if single:
        return 1
    return 0


def _dora_multi(tile: int, doraTiles: Sequence[int]) -> float:
    if tile in doraTiles:
        return DORA_RISK_MULTI['dora']
    # 邻近 dora
    if tile < 27:
        t9 = tile % 9
        for d in doraTiles:
            if d < 27 and (tile // 9) == (d // 9):
                d9 = d % 9
                if abs(t9 - d9) == 1:
                    return DORA_RISK_MULTI['near1']
                if abs(t9 - d9) == 2:
                    return DORA_RISK_MULTI['near2']
    return DORA_RISK_MULTI['none']


@dataclass
class RiskTiles34:
    values: List[float]
    def FixWithEarlyOutside(self, earlyOutsideTiles: Sequence[int]) -> 'RiskTiles34':
        # 早外：序盘打出端牌，降低相关两面风险
        if not earlyOutsideTiles:
            return self
        newv = self.values[:]
        for t in earlyOutsideTiles:
            if t < 27:
                t9 = t % 9
                base = t - t9
                if t9 <= 6:  # 影响 t+1,t+2
                    for x in (t + 1, t + 2):
                        if base <= x < base + 9:
                            newv[x] *= 0.92
                if t9 >= 2:  # 影响 t-1,t-2
                    for x in (t - 1, t - 2):
                        if base <= x < base + 9:
                            newv[x] *= 0.92
        return RiskTiles34(newv)
    def FixWithPoint(self, ronPoint: float) -> 'RiskTiles34':
        # 以对手预期荣和点数修正风险：点数越高，等效危险度越高
        if ronPoint <= 0:
            return self
        multi = 1.0
        if ronPoint >= 12000:
            multi = 1.15
        elif ronPoint >= 8000:
            multi = 1.10
        elif ronPoint >= 5200:
            multi = 1.05
        return RiskTiles34([v * multi for v in self.values])


def calculate_risk_tiles34(turns: int, safeTiles34: Sequence[bool], leftTiles34: Sequence[int], doraTiles: Sequence[int], roundWindTile: int, playerWindTile: int, is_sanma: bool = False) -> RiskTiles34:
    t = _clip_turn(turns)
    risk = [0.0] * 34
    # 数牌：根据筋类型与 dora 修正
    for idx in range(27):
        # 三麻时，2-8m (1-7) 的风险为0
        if is_sanma and 1 <= idx <= 7:
            risk[idx] = 0.0
            continue
        if leftTiles34[idx] <= 0:
            risk[idx] = 0.0
            continue
        if safeTiles34[idx]:
            risk[idx] = 0.1  # 现物近似安全
            continue
        suji_type = _suji_type_for_idx(idx, safeTiles34, leftTiles34)
        base = BASE_SUJI_RISK_BY_TURN[suji_type][t]
        risk[idx] = base * _dora_multi(idx, doraTiles)
    # 字牌：基于是否役牌，简单给定基准
    for idx in range(27, 34):
        if leftTiles34[idx] <= 0:
            risk[idx] = 0.0
            continue
        if safeTiles34[idx]:
            risk[idx] = 0.1
            continue
        base = 6.0
        # 役牌更危险
        if idx >= 31 or idx == roundWindTile or idx == playerWindTile:
            base = 8.0
        risk[idx] = base * _dora_multi(idx, doraTiles)
    return RiskTiles34(risk)


def mixed_risk_table(risk_tables: Sequence[RiskTiles34], tenpai_rates: Sequence[float]) -> List[float]:
    # 综合危险度：p = 1 - Π(1 - r_i * tenpaiRate_i)
    n = 34
    mixed = [0.0] * n
    for i in range(n):
        safe_prob = 1.0
        for rt, tr in zip(risk_tables, tenpai_rates):
            p = (rt.values[i] / 100.0) * (tr / 100.0)
            safe_prob *= (1.0 - p)
        mixed[i] = (1.0 - safe_prob) * 100.0
    return mixed


def best_defense_tile(tiles34: Sequence[int], mixed_risk: Sequence[float]) -> int:
    best = -1
    minrisk = 1e9
    for t, c in enumerate(tiles34):
        if c <= 0:
            continue
        if mixed_risk[t] < minrisk:
            minrisk = mixed_risk[t]
            best = t
    return best 