"""
自动化测试运行器
"""
import unittest
import sys
import os
import time
from io import StringIO

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("日本麻将助手 Python版 自动化测试")
    print("=" * 60)
    
    # 发现并运行所有测试
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 创建测试运行器
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        buffer=True
    )
    
    # 运行测试
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # 输出结果
    output = stream.getvalue()
    print(output)
    
    # 统计信息
    print("\n" + "=" * 60)
    print("测试统计")
    print("=" * 60)
    print(f"运行时间: {end_time - start_time:.2f}秒")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    # 详细失败信息
    if result.failures:
        print("\n" + "=" * 60)
        print("失败详情")
        print("=" * 60)
        for test, traceback in result.failures:
            print(f"\n❌ {test}")
            print(traceback)
    
    # 详细错误信息
    if result.errors:
        print("\n" + "=" * 60)
        print("错误详情")
        print("=" * 60)
        for test, traceback in result.errors:
            print(f"\n💥 {test}")
            print(traceback)
    
    # 总结
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ 所有测试通过！")
        return True
    else:
        print("❌ 存在测试失败或错误")
        return False

def run_specific_test(test_name):
    """运行特定测试"""
    print(f"运行测试: {test_name}")
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromName(test_name)
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def run_test_category(category):
    """运行特定类别的测试"""
    categories = {
        'point': 'test_point_calculation.py',
        'yaku': 'test_yaku_detection.py',
        'shanten': 'test_shanten_analysis.py',
    }
    
    if category not in categories:
        print(f"未知测试类别: {category}")
        print(f"可用类别: {', '.join(categories.keys())}")
        return False
    
    print(f"运行 {category} 测试...")
    
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)
    suite = loader.discover(start_dir, pattern=categories[category])
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 运行所有测试
        success = run_all_tests()
        sys.exit(0 if success else 1)
    
    elif len(sys.argv) == 2:
        arg = sys.argv[1]
        
        if arg in ['point', 'yaku', 'shanten']:
            # 运行特定类别测试
            success = run_test_category(arg)
            sys.exit(0 if success else 1)
        
        elif arg.startswith('test_'):
            # 运行特定测试文件
            success = run_specific_test(arg)
            sys.exit(0 if success else 1)
        
        else:
            print("用法:")
            print("  python run_tests.py                    # 运行所有测试")
            print("  python run_tests.py point              # 运行点数计算测试")
            print("  python run_tests.py yaku               # 运行役种判定测试")
            print("  python run_tests.py shanten            # 运行向听数分析测试")
            print("  python run_tests.py test_point_calculation  # 运行特定测试")
            sys.exit(1)
    
    else:
        print("参数过多")
        sys.exit(1)

if __name__ == '__main__':
    main()
