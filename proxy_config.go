package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strings"
	"sync"
)

// ProxyConfig 代理配置结构体
type ProxyConfig struct {
	Proxy         ProxySettings         `json:"proxy"`
	MahjongHelper MahjongHelperSettings `json:"mahjong_helper"`
	Logging       LoggingSettings       `json:"logging"`
	Majsoul       MajsoulSettings       `json:"majsoul"`
	Advanced      AdvancedSettings      `json:"advanced"`
	
	configFile string
	mutex      sync.RWMutex
	watchers   []ConfigWatcher
}

// ProxySettings 代理服务器设置
type ProxySettings struct {
	Host              string `json:"host"`
	Port              int    `json:"port"`
	MaxConnections    int    `json:"max_connections"`
	ConnectionTimeout int    `json:"connection_timeout"`
}

// MahjongHelperSettings 麻将助手设置
type MahjongHelperSettings struct {
	URL        string  `json:"url"`
	Timeout    float64 `json:"timeout"`
	RetryCount int     `json:"retry_count"`
	RetryDelay float64 `json:"retry_delay"`
}

// LoggingSettings 日志设置
type LoggingSettings struct {
	Level       string `json:"level"`
	File        string `json:"file"`
	MaxSize     string `json:"max_size"`
	BackupCount int    `json:"backup_count"`
}

// MajsoulSettings 雀魂设置
type MajsoulSettings struct {
	Hosts      []string           `json:"hosts"`
	Ports      []int              `json:"ports"`
	DataFilter MajsoulDataFilter  `json:"data_filter"`
}

// MajsoulDataFilter 雀魂数据过滤设置
type MajsoulDataFilter struct {
	MinPacketSize            int  `json:"min_packet_size"`
	MaxPacketSize            int  `json:"max_packet_size"`
	EnableWebSocketParsing   bool `json:"enable_websocket_parsing"`
	EnableJSONParsing        bool `json:"enable_json_parsing"`
	EnableProtobufParsing    bool `json:"enable_protobuf_parsing"`
}

// AdvancedSettings 高级设置
type AdvancedSettings struct {
	BufferSize int  `json:"buffer_size"`
	KeepAlive  bool `json:"keep_alive"`
	TCPNoDelay bool `json:"tcp_nodelay"`
}

// ConfigWatcher 配置变更监听器
type ConfigWatcher func(oldConfig, newConfig *ProxyConfig)

// NewProxyConfig 创建新的配置管理器
func NewProxyConfig(configFile string) *ProxyConfig {
	config := &ProxyConfig{
		configFile: configFile,
		watchers:   make([]ConfigWatcher, 0),
	}
	
	config.loadConfig()
	return config
}

// loadConfig 加载配置文件
func (c *ProxyConfig) loadConfig() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	// 尝试加载配置文件
	if _, err := os.Stat(c.configFile); os.IsNotExist(err) {
		fmt.Printf("配置文件不存在，使用默认配置: %s\n", c.configFile)
		c.setDefaultConfig()
		c.saveConfigInternal()
		return
	}
	
	data, err := ioutil.ReadFile(c.configFile)
	if err != nil {
		fmt.Printf("读取配置文件失败: %v，使用默认配置\n", err)
		c.setDefaultConfig()
		return
	}
	
	err = json.Unmarshal(data, c)
	if err != nil {
		fmt.Printf("解析配置文件失败: %v，使用默认配置\n", err)
		c.setDefaultConfig()
		return
	}
	
	// 验证配置
	c.validateConfig()
	
	fmt.Printf("配置文件加载成功: %s\n", c.configFile)
}

// setDefaultConfig 设置默认配置
func (c *ProxyConfig) setDefaultConfig() {
	c.Proxy = ProxySettings{
		Host:              "127.0.0.1",
		Port:              8888,
		MaxConnections:    100,
		ConnectionTimeout: 10,
	}
	
	c.MahjongHelper = MahjongHelperSettings{
		URL:        "http://localhost:12121/majsoul",
		Timeout:    1.0,
		RetryCount: 3,
		RetryDelay: 0.5,
	}
	
	c.Logging = LoggingSettings{
		Level:       "INFO",
		File:        "proxy.log",
		MaxSize:     "10MB",
		BackupCount: 5,
	}
	
	c.Majsoul = MajsoulSettings{
		Hosts: []string{
			"majsoul.com",
			"maj-soul.com",
			"mahjongsoul.game.yo-star.com",
			"game.mahjongsoul.com",
		},
		Ports: []int{443, 4130, 4131, 4132, 4133, 4134, 4135},
		DataFilter: MajsoulDataFilter{
			MinPacketSize:            10,
			MaxPacketSize:            65536,
			EnableWebSocketParsing:   true,
			EnableJSONParsing:        true,
			EnableProtobufParsing:    false,
		},
	}
	
	c.Advanced = AdvancedSettings{
		BufferSize: 8192,
		KeepAlive:  true,
		TCPNoDelay: true,
	}
}

// validateConfig 验证配置有效性
func (c *ProxyConfig) validateConfig() {
	// 端口范围检查
	if c.Proxy.Port < 1024 || c.Proxy.Port > 65535 {
		fmt.Printf("代理端口 %d 超出范围，使用默认端口 8888\n", c.Proxy.Port)
		c.Proxy.Port = 8888
	}
	
	// URL格式检查
	if !strings.HasPrefix(c.MahjongHelper.URL, "http") {
		fmt.Printf("助手URL格式错误，使用默认URL\n")
		c.MahjongHelper.URL = "http://localhost:12121/majsoul"
	}
	
	// 超时时间检查
	if c.Proxy.ConnectionTimeout <= 0 {
		fmt.Printf("连接超时时间无效，使用默认值 10 秒\n")
		c.Proxy.ConnectionTimeout = 10
	}
	
	// 缓冲区大小检查
	if c.Advanced.BufferSize <= 0 {
		fmt.Printf("缓冲区大小无效，使用默认值 8192\n")
		c.Advanced.BufferSize = 8192
	}
	
	// 数据包大小检查
	if c.Majsoul.DataFilter.MinPacketSize <= 0 {
		c.Majsoul.DataFilter.MinPacketSize = 10
	}
	
	if c.Majsoul.DataFilter.MaxPacketSize <= c.Majsoul.DataFilter.MinPacketSize {
		c.Majsoul.DataFilter.MaxPacketSize = 65536
	}
	
	// 雀魂主机列表检查
	if len(c.Majsoul.Hosts) == 0 {
		fmt.Printf("雀魂主机列表为空，使用默认列表\n")
		c.Majsoul.Hosts = []string{
			"majsoul.com",
			"maj-soul.com",
			"mahjongsoul.game.yo-star.com",
			"game.mahjongsoul.com",
		}
	}
}

// SaveConfig 保存配置到文件
func (c *ProxyConfig) SaveConfig() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	return c.saveConfigInternal()
}

// saveConfigInternal 内部保存配置方法（不加锁）
func (c *ProxyConfig) saveConfigInternal() error {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}
	
	err = ioutil.WriteFile(c.configFile, data, 0644)
	if err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}
	
	fmt.Printf("配置已保存到: %s\n", c.configFile)
	return nil
}

// WatchConfig 监听配置变更
func (c *ProxyConfig) WatchConfig(watcher ConfigWatcher) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	c.watchers = append(c.watchers, watcher)
}

// ReloadConfig 重新加载配置
func (c *ProxyConfig) ReloadConfig() error {
	c.mutex.Lock()
	oldConfig := c.clone()
	c.mutex.Unlock()
	
	c.loadConfig()
	
	// 通知监听器
	c.mutex.RLock()
	newConfig := c.clone()
	watchers := make([]ConfigWatcher, len(c.watchers))
	copy(watchers, c.watchers)
	c.mutex.RUnlock()
	
	for _, watcher := range watchers {
		go func(w ConfigWatcher) {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("配置变更回调异常: %v\n", r)
				}
			}()
			w(oldConfig, newConfig)
		}(watcher)
	}
	
	return nil
}

// clone 克隆配置对象
func (c *ProxyConfig) clone() *ProxyConfig {
	data, _ := json.Marshal(c)
	var cloned ProxyConfig
	json.Unmarshal(data, &cloned)
	return &cloned
}

// GetProxyAddress 获取代理地址
func (c *ProxyConfig) GetProxyAddress() string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	return fmt.Sprintf("%s:%d", c.Proxy.Host, c.Proxy.Port)
}

// IsMajsoulHost 检查是否为雀魂主机
func (c *ProxyConfig) IsMajsoulHost(host string) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	for _, majsoulHost := range c.Majsoul.Hosts {
		if strings.Contains(host, majsoulHost) {
			return true
		}
	}
	return false
}

// GetMahjongHelperURL 获取麻将助手URL
func (c *ProxyConfig) GetMahjongHelperURL() string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	return c.MahjongHelper.URL
}
