# 错误处理设计

## 1. 连接错误处理

### 目标服务器不可达
```python
try:
    remote_socket.connect((host, port))
except socket.timeout:
    # 返回502 Bad Gateway
    client_socket.send(b'HTTP/1.1 502 Bad Gateway\r\n\r\n')
except ConnectionRefusedError:
    # 返回503 Service Unavailable  
    client_socket.send(b'HTTP/1.1 503 Service Unavailable\r\n\r\n')
```

### 助手连接失败
```python
class MahjongHelperClient:
    def send_data(self, data):
        try:
            response = self.session.post(self.helper_url, data=data)
            return response.status_code == 200
        except requests.exceptions.ConnectionError:
            # 助手未启动，记录日志但不影响代理功能
            logger.warning("助手连接失败，跳过数据发送")
            return False
        except requests.exceptions.Timeout:
            # 超时，避免阻塞代理
            logger.warning("助手响应超时")
            return False
```

## 2. 数据传输错误

### 连接中断处理
```python
def forward_data(source, destination):
    try:
        while True:
            data = source.recv(8192)
            if not data:  # 连接关闭
                break
            destination.send(data)
    except socket.error as e:
        logger.debug(f"连接异常: {e}")
    finally:
        # 确保资源清理
        try:
            source.close()
            destination.close()
        except:
            pass
```

## 3. 资源管理

### 线程管理
```python
class MajsoulProxy:
    def __init__(self):
        self.active_connections = []
        self.shutdown_event = threading.Event()
    
    def handle_client(self, client_socket):
        conn_info = {
            'socket': client_socket,
            'thread': threading.current_thread(),
            'start_time': time.time()
        }
        self.active_connections.append(conn_info)
        
        try:
            # 处理连接
            pass
        finally:
            self.active_connections.remove(conn_info)
    
    def shutdown(self):
        self.shutdown_event.set()
        # 等待所有连接关闭
        for conn in self.active_connections:
            conn['socket'].close()
```