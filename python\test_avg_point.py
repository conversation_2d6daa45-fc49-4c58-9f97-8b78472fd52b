from mahjong_py import str_to_tiles34, calculate_shanten, analyze_13
from mahjong_py.avg_point import average_point_and_rate, calc_avg_point
from mahjong_py.agari_rate import calculate_agari_rate_each_tile, calculate_avg_agari_rate

# 测试平均点数和和率计算
print("Testing average point and rate calculation...")

# 测试用例：听牌状态
tiles34, _ = str_to_tiles34("123m 456m 789m 123p 1z")  # 13张，听1z
print(f"tiles34: {tiles34}")
print(f"sum: {sum(tiles34)}")

# 分析手牌
r13 = analyze_13(tiles34)
print(f"shanten: {r13.shanten}")
print(f"waits: {r13.waits.counts}")
print(f"total waits: {r13.waits.all_count()}")

# 测试基础平均点数和和率
avg_pt, avg_rate = average_point_and_rate(
    tiles34[:], r13.waits.counts,
    is_tsumo=False, is_parent=False, 
    round_wind=27, self_wind=27
)
print(f"Basic avg point: {avg_pt}, avg rate: {avg_rate}%")

# 测试更精确的和率计算
rates = calculate_agari_rate_each_tile(r13.waits.counts)
print(f"Individual tile rates: {rates}")

total_rate = calculate_avg_agari_rate(r13.waits.counts)
print(f"Total agari rate: {total_rate}%")

# 测试高级平均点数计算
adv_avg_pt, tile_rates = calc_avg_point(
    tiles34[:], r13.waits.counts,
    is_parent=False, is_riichi=False, is_tsumo=False,
    round_wind=27, self_wind=27
)
print(f"Advanced avg point: {adv_avg_pt}")
print(f"Tile rates: {tile_rates}")

# 测试立直时的计算
print("\nTesting riichi scenario...")
tiles34_riichi, _ = str_to_tiles34("234m 567m 789p 123s 1z")  # 听1z
r13_riichi = analyze_13(tiles34_riichi)
print(f"Riichi waits: {r13_riichi.waits.counts}")

riichi_rates = calculate_agari_rate_each_tile(r13_riichi.waits.counts)
print(f"Riichi tile rates: {riichi_rates}")

# 测试立直平均点数计算
from mahjong_py.avg_point import calc_avg_riichi_point
riichi_avg_pt, riichi_tile_rates = calc_avg_riichi_point(
    tiles34_riichi[:], r13_riichi.waits.counts,
    is_parent=False, round_wind=27, self_wind=27
)
print(f"Riichi avg point: {riichi_avg_pt}")
print(f"Riichi tile rates: {riichi_tile_rates}")

# 对比普通和立直的点数差异
normal_avg_pt, _ = calc_avg_point(
    tiles34_riichi[:], r13_riichi.waits.counts,
    is_parent=False, is_riichi=False, is_tsumo=False,
    round_wind=27, self_wind=27
)
print(f"Normal avg point: {normal_avg_pt}")
print(f"Riichi bonus: {riichi_avg_pt - normal_avg_pt:.1f} points")
