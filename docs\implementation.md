# 实现细节

## 1. CONNECT请求解析

```python
def parse_connect_request(request_data):
    """
    解析CONNECT请求
    
    示例请求:
    CONNECT majsoul.com:443 HTTP/1.1
    Host: majsoul.com:443
    Proxy-Connection: keep-alive
    """
    lines = request_data.decode('utf-8').split('\n')
    connect_line = lines[0].strip()
    
    if not connect_line.startswith('CONNECT'):
        raise ValueError("非CONNECT请求")
    
    parts = connect_line.split(' ')
    if len(parts) < 2:
        raise ValueError("CONNECT请求格式错误")
    
    target = parts[1]
    if ':' not in target:
        raise ValueError("目标地址格式错误")
    
    host, port = target.rsplit(':', 1)
    return host, int(port)
```

## 2. 双向数据转发

```python
def start_forwarding(self, client_socket, remote_socket, is_majsoul):
    """启动双向数据转发"""
    
    def client_to_server():
        """客户端 → 服务器"""
        try:
            while True:
                data = client_socket.recv(8192)
                if not data:
                    break
                remote_socket.send(data)
        except Exception as e:
            logger.debug(f"客户端→服务器转发异常: {e}")
        finally:
            remote_socket.close()
    
    def server_to_client():
        """服务器 → 客户端"""
        try:
            while True:
                data = remote_socket.recv(8192)
                if not data:
                    break
                
                # 雀魂数据拦截
                if is_majsoul:
                    self.intercept_majsoul_data(data)
                
                client_socket.send(data)
        except Exception as e:
            logger.debug(f"服务器→客户端转发异常: {e}")
        finally:
            client_socket.close()
    
    # 启动转发线程
    threading.Thread(target=client_to_server, daemon=True).start()
    threading.Thread(target=server_to_client, daemon=True).start()
```

## 3. 数据拦截与处理

```python
def intercept_majsoul_data(self, data):
    """拦截雀魂数据"""
    
    # 1. 数据包大小过滤
    if len(data) < 10:
        return
    
    # 2. 协议特征识别
    if self.is_websocket_frame(data):
        # WebSocket帧处理
        payload = self.extract_websocket_payload(data)
        if payload:
            self.send_to_helper(payload)
    
    elif self.is_json_data(data):
        # 直接JSON数据
        self.send_to_helper(data)
    
    elif self.is_protobuf_data(data):
        # protobuf数据
        self.send_to_helper(data)

def is_websocket_frame(self, data):
    """检查是否为WebSocket帧"""
    if len(data) < 2:
        return False
    
    # WebSocket帧格式检查
    fin = (data[0] & 0x80) != 0
    opcode = data[0] & 0x0f
    masked = (data[1] & 0x80) != 0
    
    # 基本格式验证
    return opcode in [0x1, 0x2, 0x8, 0x9, 0xa]  # text, binary, close, ping, pong

def extract_websocket_payload(self, data):
    """提取WebSocket载荷"""
    if len(data) < 2:
        return None
    
    payload_len = data[1] & 0x7f
    header_len = 2
    
    if payload_len == 126:
        if len(data) < 4:
            return None
        payload_len = struct.unpack('>H', data[2:4])[0]
        header_len = 4
    elif payload_len == 127:
        if len(data) < 10:
            return None
        payload_len = struct.unpack('>Q', data[2:10])[0]
        header_len = 10
    
    # 检查掩码
    masked = (data[1] & 0x80) != 0
    if masked:
        if len(data) < header_len + 4:
            return None
        mask = data[header_len:header_len + 4]
        header_len += 4
        
        if len(data) < header_len + payload_len:
            return None
        
        payload = bytearray(data[header_len:header_len + payload_len])
        for i in range(len(payload)):
            payload[i] ^= mask[i % 4]
        return bytes(payload)
    else:
        if len(data) < header_len + payload_len:
            return None
        return data[header_len:header_len + payload_len]
```