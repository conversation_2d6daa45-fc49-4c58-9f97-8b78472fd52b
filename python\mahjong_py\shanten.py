from __future__ import annotations
from typing import List

SHANTEN_AGARI = -1


def calculate_shanten_of_chiitoi(tiles34: List[int]) -> int:
    shanten = 6
    kinds = 0
    for c in tiles34:
        if c == 0:
            continue
        if c >= 2:
            shanten -= 1
        kinds += 1
    shanten += max(0, 7 - kinds)
    return shanten


def _scan_honors(tiles: List[int], count: int):
    number_melds = 0
    number_pairs = 0
    number_jidahai = 0
    ankan_bits = 0
    isolated_bits = 0
    for i, c in enumerate(tiles[27:]):
        if c == 0:
            continue
        if c == 1:
            isolated_bits |= 1 << i
        elif c == 2:
            number_pairs += 1
        elif c == 3:
            number_melds += 1
        elif c == 4:
            number_melds += 1
            number_jidahai += 1
            ankan_bits |= 1 << i
            isolated_bits |= 1 << i
    if number_jidahai > 0 and count % 3 == 2:
        number_jidahai -= 1
    if isolated_bits:
        isolated_bits |= 1 << 27
        if (ankan_bits | isolated_bits) == ankan_bits:
            ankan_bits |= 1 << 27
    return number_melds, number_pairs, number_jidahai, ankan_bits, isolated_bits


def _calc_normal_shanten(tiles: List[int], number_melds: int, number_pairs: int, number_tatsu: int,
                         number_jidahai: int, ankan_bits: int, isolated_bits: int) -> int:
    shanten = 8 - 2 * number_melds - number_tatsu - number_pairs
    mentsu_kouho = number_melds + number_tatsu
    if number_pairs > 0:
        mentsu_kouho += number_pairs - 1
    else:
        if ankan_bits > 0 and isolated_bits > 0 and (ankan_bits | isolated_bits) == ankan_bits:
            shanten += 1
    if mentsu_kouho > 4:
        shanten += mentsu_kouho - 4
    if shanten != SHANTEN_AGARI and shanten < number_jidahai:
        return number_jidahai
    return shanten


def calculate_shanten(tiles34: List[int]) -> int:
    count = sum(tiles34)
    if count > 14:
        raise ValueError("too many tiles (>14)")

    # honors pre-scan
    nm, np, nj, ankan, iso = _scan_honors(tiles34, count)

    # pre-mark ankan for numbers
    for i, c in enumerate(tiles34[:27]):
        if c == 4:
            ankan |= 1 << i

    min_shanten = 8

    def dfs(depth: int, tiles: List[int], number_melds: int, number_pairs: int, number_tatsu: int,
            ankan_bits: int, isolated_bits: int):
        nonlocal min_shanten
        if min_shanten == SHANTEN_AGARI:
            return
        # skip empty
        while depth < 27 and tiles[depth] == 0:
            depth += 1
        if depth >= 27:
            s = _calc_normal_shanten(tiles, number_melds, number_pairs, number_tatsu, nj, ankan_bits, isolated_bits)
            if s < min_shanten:
                min_shanten = s
            return
        # fast mod-9 index
        i9 = depth
        if i9 > 8:
            i9 -= 9
        if i9 > 8:
            i9 -= 9
        c = tiles[depth]
        if c == 1:
            # try float
            tiles[depth] -= 1
            isolated_bits |= 1 << depth
            dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
            isolated_bits &= ~(1 << depth)
            tiles[depth] += 1
            # tatsu second (kanchan)
            if i9 < 7 and tiles[depth + 2] > 0:
                tiles[depth] -= 1
                tiles[depth + 2] -= 1
                number_tatsu += 1
                dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_tatsu -= 1
                tiles[depth] += 1
                tiles[depth + 2] += 1
            # tatsu first (ryanmen/penchan)
            if i9 < 8 and tiles[depth + 1] > 0:
                tiles[depth] -= 1
                tiles[depth + 1] -= 1
                number_tatsu += 1
                dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_tatsu -= 1
                tiles[depth] += 1
                tiles[depth + 1] += 1
            # nobetan shortcut to shuntsu
            if i9 < 6 and tiles[depth + 1] == 1 and tiles[depth + 2] > 0 and tiles[depth + 3] < 4:
                tiles[depth] -= 1
                tiles[depth + 1] -= 1
                tiles[depth + 2] -= 1
                number_melds += 1
                dfs(depth + 2, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_melds -= 1
                tiles[depth] += 1
                tiles[depth + 1] += 1
                tiles[depth + 2] += 1
        elif c == 2:
            # pair
            tiles[depth] -= 2
            number_pairs += 1
            dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
            number_pairs -= 1
            tiles[depth] += 2
            # shuntsu - when forming a sequence, we need to recheck current position
            if i9 < 7 and tiles[depth + 1] > 0 and tiles[depth + 2] > 0:
                tiles[depth] -= 1
                tiles[depth + 1] -= 1
                tiles[depth + 2] -= 1
                number_melds += 1
                dfs(depth, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_melds -= 1
                tiles[depth] += 1
                tiles[depth + 1] += 1
                tiles[depth + 2] += 1
        elif c == 3:
            # set (triplet)
            tiles[depth] -= 3
            number_melds += 1
            dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
            number_melds -= 1
            tiles[depth] += 3

            # pair + various combinations
            tiles[depth] -= 2
            number_pairs += 1
            if i9 < 7 and tiles[depth + 1] > 0 and tiles[depth + 2] > 0:
                # pair + shuntsu
                tiles[depth] -= 1
                tiles[depth + 1] -= 1
                tiles[depth + 2] -= 1
                number_melds += 1
                dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_melds -= 1
                tiles[depth] += 1
                tiles[depth + 1] += 1
                tiles[depth + 2] += 1
            else:
                if i9 < 7 and tiles[depth + 2] > 0:
                    # pair + kanchan tatsu
                    tiles[depth] -= 1
                    tiles[depth + 2] -= 1
                    number_tatsu += 1
                    dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                    number_tatsu -= 1
                    tiles[depth] += 1
                    tiles[depth + 2] += 1
                if i9 < 8 and tiles[depth + 1] > 0:
                    # pair + ryanmen/penchan tatsu
                    tiles[depth] -= 1
                    tiles[depth + 1] -= 1
                    number_tatsu += 1
                    dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                    number_tatsu -= 1
                    tiles[depth] += 1
                    tiles[depth + 1] += 1
            number_pairs -= 1
            tiles[depth] += 2

            # iipeikou (double sequence) - when we have enough tiles
            if i9 < 7 and tiles[depth + 1] >= 2 and tiles[depth + 2] >= 2:
                tiles[depth] -= 2
                tiles[depth + 1] -= 2
                tiles[depth + 2] -= 2
                number_melds += 2
                dfs(depth, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_melds -= 2
                tiles[depth] += 2
                tiles[depth + 1] += 2
                tiles[depth + 2] += 2
        elif c == 4:
            # case 4: exactly 4 tiles at this position
            # First try: triplet (anko) + remaining tile
            tiles[depth] -= 3
            number_melds += 1

            # Try various combinations with the remaining tile
            if i9 < 7 and tiles[depth + 2] > 0:
                if tiles[depth + 1] > 0:
                    # triplet + shuntsu
                    tiles[depth] -= 1
                    tiles[depth + 1] -= 1
                    tiles[depth + 2] -= 1
                    number_melds += 1
                    dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                    number_melds -= 1
                    tiles[depth] += 1
                    tiles[depth + 1] += 1
                    tiles[depth + 2] += 1
                # triplet + kanchan tatsu
                tiles[depth] -= 1
                tiles[depth + 2] -= 1
                number_tatsu += 1
                dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_tatsu -= 1
                tiles[depth] += 1
                tiles[depth + 2] += 1
            if i9 < 8 and tiles[depth + 1] > 0:
                # triplet + ryanmen/penchan tatsu
                tiles[depth] -= 1
                tiles[depth + 1] -= 1
                number_tatsu += 1
                dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_tatsu -= 1
                tiles[depth] += 1
                tiles[depth + 1] += 1
            # triplet + isolated tile
            tiles[depth] -= 1
            isolated_bits |= 1 << depth
            dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
            isolated_bits &= ~(1 << depth)
            tiles[depth] += 1

            number_melds -= 1
            tiles[depth] += 3

            # Second try: pair + remaining tiles
            tiles[depth] -= 2
            number_pairs += 1

            if i9 < 7 and tiles[depth + 2] > 0:
                if tiles[depth + 1] > 0:
                    # pair + shuntsu (recheck current position)
                    tiles[depth] -= 1
                    tiles[depth + 1] -= 1
                    tiles[depth + 2] -= 1
                    number_melds += 1
                    dfs(depth, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                    number_melds -= 1
                    tiles[depth] += 1
                    tiles[depth + 1] += 1
                    tiles[depth + 2] += 1
                # pair + kanchan tatsu
                tiles[depth] -= 1
                tiles[depth + 2] -= 1
                number_tatsu += 1
                dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_tatsu -= 1
                tiles[depth] += 1
                tiles[depth + 2] += 1
            if i9 < 8 and tiles[depth + 1] > 0:
                # pair + ryanmen/penchan tatsu
                tiles[depth] -= 1
                tiles[depth + 1] -= 1
                number_tatsu += 1
                dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)
                number_tatsu -= 1
                tiles[depth] += 1
                tiles[depth + 1] += 1

            number_pairs -= 1
            tiles[depth] += 2
        else:
            # c==0
            dfs(depth + 1, tiles, number_melds, number_pairs, number_tatsu, ankan_bits, isolated_bits)

    dfs(0, tiles34[:], nm, np, 0, ankan, iso)

    if count >= 13:
        min_shanten = min(min_shanten, calculate_shanten_of_chiitoi(tiles34))
    return min_shanten 