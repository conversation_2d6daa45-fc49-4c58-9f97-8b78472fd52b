from __future__ import annotations
from functools import lru_cache
from typing import List


def _can_form_sets(nums: List[int]) -> bool:
    # nums length is 9 (one suit) or 7 (honors)
    @lru_cache(maxsize=None)
    def dfs(t: tuple) -> bool:
        arr = list(t)
        # skip zeros
        i = 0
        while i < len(arr) and arr[i] == 0:
            i += 1
        if i == len(arr):
            return True
        # try triplet
        if arr[i] >= 3:
            arr[i] -= 3
            if dfs(tuple(arr)):
                return True
            arr[i] += 3
        # try sequence for number tiles only (len==9)
        if len(arr) == 9 and i <= 6 and arr[i + 1] > 0 and arr[i + 2] > 0:
            arr[i] -= 1
            arr[i + 1] -= 1
            arr[i + 2] -= 1
            if dfs(tuple(arr)):
                return True
        return False

    return dfs(tuple(nums))


def is_agari(tiles34: List[int]) -> bool:
    if sum(tiles34) % 3 != 2:
        return False

    # 检查七对子
    if sum(tiles34) == 14:
        pair_count = 0
        for c in tiles34:
            if c == 2:
                pair_count += 1
            elif c != 0:
                break
        else:
            if pair_count == 7:
                return True

    # try each possible pair
    for i, c in enumerate(tiles34):
        if c >= 2:
            tiles34[i] -= 2
            ok = True
            # suits
            for s in range(3):
                block = tiles34[s * 9 : s * 9 + 9]
                if not _can_form_sets(block):
                    ok = False
                    break
            # honors
            if ok:
                honors = tiles34[27:34]
                if not _can_form_sets(honors):
                    ok = False
            tiles34[i] += 2
            if ok:
                return True
    return False