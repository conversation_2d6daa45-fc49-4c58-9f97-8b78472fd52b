package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	LogLevelDebug LogLevel = iota
	LogLevelInfo
	LogLevelWarn
	LogLevelError
	LogLevelFatal
)

// String 返回日志级别的字符串表示
func (l LogLevel) String() string {
	switch l {
	case LogLevelDebug:
		return "DEBUG"
	case LogLevelInfo:
		return "INFO"
	case LogLevelWarn:
		return "WARN"
	case LogLevelError:
		return "ERROR"
	case LogLevelFatal:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// ProxyLogger 代理日志器
type ProxyLogger struct {
	level      LogLevel
	file       *os.File
	logger     *log.Logger
	mutex      sync.Mutex
	config     *ProxyConfig
	maxSize    int64
	backupCount int
}

// NewProxyLogger 创建新的日志器
func NewProxyLogger(config *ProxyConfig) (*ProxyLogger, error) {
	logger := &ProxyLogger{
		config:      config,
		level:       parseLogLevel(config.Logging.Level),
		maxSize:     parseMaxSize(config.Logging.MaxSize),
		backupCount: config.Logging.BackupCount,
	}
	
	err := logger.initLogFile()
	if err != nil {
		return nil, fmt.Errorf("初始化日志文件失败: %v", err)
	}
	
	return logger, nil
}

// initLogFile 初始化日志文件
func (l *ProxyLogger) initLogFile() error {
	// 创建日志目录
	logDir := filepath.Dir(l.config.Logging.File)
	if logDir != "." {
		err := os.MkdirAll(logDir, 0755)
		if err != nil {
			return fmt.Errorf("创建日志目录失败: %v", err)
		}
	}
	
	// 打开日志文件
	file, err := os.OpenFile(l.config.Logging.File, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}
	
	l.file = file
	
	// 创建多输出writer（同时输出到文件和控制台）
	multiWriter := io.MultiWriter(file, os.Stdout)
	l.logger = log.New(multiWriter, "", log.LstdFlags|log.Lmicroseconds)
	
	return nil
}

// parseLogLevel 解析日志级别
func parseLogLevel(levelStr string) LogLevel {
	switch strings.ToUpper(levelStr) {
	case "DEBUG":
		return LogLevelDebug
	case "INFO":
		return LogLevelInfo
	case "WARN", "WARNING":
		return LogLevelWarn
	case "ERROR":
		return LogLevelError
	case "FATAL":
		return LogLevelFatal
	default:
		return LogLevelInfo
	}
}

// parseMaxSize 解析最大文件大小
func parseMaxSize(sizeStr string) int64 {
	sizeStr = strings.ToUpper(sizeStr)
	
	var multiplier int64 = 1
	if strings.HasSuffix(sizeStr, "KB") {
		multiplier = 1024
		sizeStr = strings.TrimSuffix(sizeStr, "KB")
	} else if strings.HasSuffix(sizeStr, "MB") {
		multiplier = 1024 * 1024
		sizeStr = strings.TrimSuffix(sizeStr, "MB")
	} else if strings.HasSuffix(sizeStr, "GB") {
		multiplier = 1024 * 1024 * 1024
		sizeStr = strings.TrimSuffix(sizeStr, "GB")
	}
	
	var size int64
	fmt.Sscanf(sizeStr, "%d", &size)
	
	if size <= 0 {
		size = 10 // 默认10MB
		multiplier = 1024 * 1024
	}
	
	return size * multiplier
}

// shouldLog 检查是否应该记录该级别的日志
func (l *ProxyLogger) shouldLog(level LogLevel) bool {
	return level >= l.level
}

// rotateLogFile 轮转日志文件
func (l *ProxyLogger) rotateLogFile() error {
	if l.file == nil {
		return nil
	}
	
	// 检查文件大小
	stat, err := l.file.Stat()
	if err != nil {
		return err
	}
	
	if stat.Size() < l.maxSize {
		return nil
	}
	
	// 关闭当前文件
	l.file.Close()
	
	// 轮转备份文件
	for i := l.backupCount - 1; i >= 1; i-- {
		oldName := fmt.Sprintf("%s.%d", l.config.Logging.File, i)
		newName := fmt.Sprintf("%s.%d", l.config.Logging.File, i+1)
		
		if _, err := os.Stat(oldName); err == nil {
			os.Rename(oldName, newName)
		}
	}
	
	// 将当前文件重命名为 .1
	if l.backupCount > 0 {
		backupName := fmt.Sprintf("%s.1", l.config.Logging.File)
		os.Rename(l.config.Logging.File, backupName)
	}
	
	// 重新初始化日志文件
	return l.initLogFile()
}

// log 记录日志
func (l *ProxyLogger) log(level LogLevel, format string, args ...interface{}) {
	if !l.shouldLog(level) {
		return
	}
	
	l.mutex.Lock()
	defer l.mutex.Unlock()
	
	// 检查是否需要轮转日志
	l.rotateLogFile()
	
	// 格式化消息
	message := fmt.Sprintf(format, args...)
	logMessage := fmt.Sprintf("[%s] %s", level.String(), message)
	
	// 写入日志
	if l.logger != nil {
		l.logger.Println(logMessage)
	}
}

// Debug 记录调试日志
func (l *ProxyLogger) Debug(format string, args ...interface{}) {
	l.log(LogLevelDebug, format, args...)
}

// Info 记录信息日志
func (l *ProxyLogger) Info(format string, args ...interface{}) {
	l.log(LogLevelInfo, format, args...)
}

// Warn 记录警告日志
func (l *ProxyLogger) Warn(format string, args ...interface{}) {
	l.log(LogLevelWarn, format, args...)
}

// Error 记录错误日志
func (l *ProxyLogger) Error(format string, args ...interface{}) {
	l.log(LogLevelError, format, args...)
}

// Fatal 记录致命错误日志并退出程序
func (l *ProxyLogger) Fatal(format string, args ...interface{}) {
	l.log(LogLevelFatal, format, args...)
	os.Exit(1)
}

// Close 关闭日志器
func (l *ProxyLogger) Close() error {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	
	if l.file != nil {
		return l.file.Close()
	}
	
	return nil
}

// SetLevel 设置日志级别
func (l *ProxyLogger) SetLevel(level LogLevel) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	
	l.level = level
}

// GetLevel 获取当前日志级别
func (l *ProxyLogger) GetLevel() LogLevel {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	
	return l.level
}

// ProxyError 代理错误类型
type ProxyError struct {
	Type      string
	Message   string
	Timestamp time.Time
	Details   map[string]interface{}
}

// Error 实现error接口
func (e *ProxyError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Type, e.Message)
}

// NewProxyError 创建新的代理错误
func NewProxyError(errorType, message string, details map[string]interface{}) *ProxyError {
	return &ProxyError{
		Type:      errorType,
		Message:   message,
		Timestamp: time.Now(),
		Details:   details,
	}
}

// 预定义的错误类型
const (
	ErrorTypeConnection    = "CONNECTION"
	ErrorTypeDataTransfer  = "DATA_TRANSFER"
	ErrorTypeConfiguration = "CONFIGURATION"
	ErrorTypeHelper        = "HELPER"
	ErrorTypeInternal      = "INTERNAL"
)

// ErrorHandler 错误处理器
type ErrorHandler struct {
	logger *ProxyLogger
}

// NewErrorHandler 创建新的错误处理器
func NewErrorHandler(logger *ProxyLogger) *ErrorHandler {
	return &ErrorHandler{
		logger: logger,
	}
}

// HandleError 处理错误
func (h *ErrorHandler) HandleError(err error) {
	if err == nil {
		return
	}
	
	if proxyErr, ok := err.(*ProxyError); ok {
		h.logger.Error("代理错误 [%s]: %s", proxyErr.Type, proxyErr.Message)
		if proxyErr.Details != nil {
			for key, value := range proxyErr.Details {
				h.logger.Debug("错误详情 %s: %v", key, value)
			}
		}
	} else {
		h.logger.Error("未知错误: %v", err)
	}
}

// HandleConnectionError 处理连接错误
func (h *ErrorHandler) HandleConnectionError(err error, host string, port int) {
	details := map[string]interface{}{
		"host": host,
		"port": port,
	}
	
	proxyErr := NewProxyError(ErrorTypeConnection, err.Error(), details)
	h.HandleError(proxyErr)
}

// HandleDataTransferError 处理数据传输错误
func (h *ErrorHandler) HandleDataTransferError(err error, connID string) {
	details := map[string]interface{}{
		"connection_id": connID,
	}
	
	proxyErr := NewProxyError(ErrorTypeDataTransfer, err.Error(), details)
	h.HandleError(proxyErr)
}

// HandleHelperError 处理助手通信错误
func (h *ErrorHandler) HandleHelperError(err error) {
	proxyErr := NewProxyError(ErrorTypeHelper, err.Error(), nil)
	h.HandleError(proxyErr)
}
