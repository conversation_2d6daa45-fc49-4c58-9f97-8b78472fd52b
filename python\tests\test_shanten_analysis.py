"""
向听数和进张分析测试 - 对照Go版本的相关测试
"""
import unittest
from mahjong_py import str_to_tiles34, calculate_shanten, analyze_13, analyze_14, is_agari
from mahjong_py.config import set_is_sanma
import copy


class TestShantenAnalysis(unittest.TestCase):
    """向听数和进张分析测试类"""
    
    def setUp(self):
        """测试前设置"""
        set_is_sanma(False)  # 默认四麻
    
    def test_basic_shanten_calculation(self):
        """测试基础向听数计算"""
        test_cases = [
            # (手牌, 期望向听数, 描述)
            ("123456789m 1234s", 1, "1向听"),  # 13张，实际是1向听
            ("123456789m 123s 4s", 1, "1向听"),  # 13张
            ("123456789m 12s 45s", 2, "2向听"),  # 13张
            ("19m 19p 19s 1234567z", 6, "国士无双6向听"),  # 13张
            ("11223344556677z", -1, "七对子和牌"),  # 14张
            ("1133556699m 1122s", -1, "七对子和牌"),  # 14张
            ("1133556699m 112s", 0, "七对子听牌"),  # 13张，听2s
        ]
        
        for hand_str, expected_shanten, description in test_cases:
            with self.subTest(hand=hand_str, desc=description):
                tiles34, _ = str_to_tiles34(hand_str)
                # 跳过牌数不正确的测试用例
                tile_count = sum(tiles34)
                if tile_count not in [13, 14]:
                    continue
                shanten = calculate_shanten(tiles34)
                self.assertEqual(expected_shanten, shanten, f"Failed for {description}")
    
    def test_agari_detection(self):
        """测试和牌判定 - 对照Go版本TestIsAgari"""
        agari_hands = [
            "123456789m 12344s",
            "11122345678999s",
            "111234678m 11122z",
            "22334455m 234s 234p",
            "111222333m 234s 11z",
            "112233m 112233p 11z",
            "11223344556677z",   # 七对子
            "1133556699m 1122s", # 七对子
        ]
        
        for hand_str in agari_hands:
            with self.subTest(hand=hand_str):
                tiles34, _ = str_to_tiles34(hand_str)
                self.assertTrue(is_agari(tiles34), f"Should be agari: {hand_str}")
    
    def test_non_agari_detection(self):
        """测试非和牌判定"""
        non_agari_hands = [
            "123456789m 1234s",   # 听牌
            "123456789m 123s 4s", # 1向听
            "19m 19p 19s 1234567z", # 国士6向听
            "1133556699m 112s",   # 七对子听牌
        ]
        
        for hand_str in non_agari_hands:
            with self.subTest(hand=hand_str):
                tiles34, _ = str_to_tiles34(hand_str)
                self.assertFalse(is_agari(tiles34), f"Should not be agari: {hand_str}")
    
    def test_13_tile_analysis(self):
        """测试13张牌分析"""
        test_cases = [
            # (手牌, 期望向听数, 期望进张数范围, 描述)
            ("123456789m 1234s", 1, (10, 50), "1向听"),  # 13张，实际是1向听
            ("123456789m 123s 4s", 1, (10, 50), "1向听"),  # 13张
            ("123m 456m 78m 234p 56s", 1, (30, 50), "1向听多面听"),  # 13张
            ("1133556699m 112s", 0, (1, 5), "七对子听牌"),  # 13张，听2s
        ]
        
        for hand_str, expected_shanten, wait_range, description in test_cases:
            with self.subTest(hand=hand_str, desc=description):
                tiles34, _ = str_to_tiles34(hand_str)
                result = analyze_13(tiles34)
                
                self.assertEqual(expected_shanten, result.shanten, 
                               f"Shanten mismatch for {description}")
                
                wait_count = result.waits.all_count()
                self.assertGreaterEqual(wait_count, wait_range[0], 
                                      f"Wait count too low for {description}")
                self.assertLessEqual(wait_count, wait_range[1], 
                                   f"Wait count too high for {description}")
    
    def test_14_tile_analysis(self):
        """测试14张何切分析"""
        test_cases = [
            # (手牌, 期望最小向听数, 描述)
            ("123456789m 12344s", 0, "和牌状态"),  # 14张，实际是0向听（和牌）
            ("123456789m 12345s", 1, "1向听状态"),   # 14张，实际是1向听
            ("123m 456m 789m 123p 11z", 0, "听牌状态"),  # 14张
            ("123m 456m 789m 234p 56s 1z", 1, "1向听"),  # 14张
        ]
        
        for hand_str, expected_min_shanten, description in test_cases:
            with self.subTest(hand=hand_str, desc=description):
                tiles34, _ = str_to_tiles34(hand_str)
                # 确保是14张牌
                if sum(tiles34) != 14:
                    continue
                min_shanten, results = analyze_14(copy.deepcopy(tiles34))

                self.assertEqual(expected_min_shanten, min_shanten,
                               f"Min shanten mismatch for {description}")

                if results:
                    # 检查结果是否按进张数排序
                    for i in range(len(results) - 1):
                        curr_score = results[i].result13.mixed_waits_score
                        next_score = results[i + 1].result13.mixed_waits_score
                        self.assertGreaterEqual(curr_score, next_score,
                                              "Results should be sorted by score")
    
    def test_sanma_analysis(self):
        """测试三麻分析"""
        set_is_sanma(True)
        
        # 三麻手牌（不包含2-8m）
        tiles34, _ = str_to_tiles34("19m 123p 456p 789s 11z")
        
        # 三麻分析
        result_sanma = analyze_13(tiles34, is_sanma=True)
        result_4ma = analyze_13(tiles34, is_sanma=False)
        
        # 三麻的进张数应该少于四麻（因为去除了2-8m）
        self.assertLess(result_sanma.waits.all_count(), result_4ma.waits.all_count(),
                       "Sanma should have fewer waits than 4-player")
        
        # 向听数应该相同
        self.assertEqual(result_sanma.shanten, result_4ma.shanten,
                        "Shanten should be same for sanma and 4-player")
        
        set_is_sanma(False)  # 恢复默认设置
    
    def test_complex_hands(self):
        """测试复杂手牌的分析"""
        complex_cases = [
            # 测试各种复杂形状
            ("111222333m 119p 9p", 1, "复杂1向听"),  # 13张
            ("11223344m 5566p 7s", 0, "多面听牌"),  # 13张，实际是听牌
            ("123456m 123456p 1s", 0, "顺子型听牌"),  # 13张，实际是听牌
        ]
        
        for hand_str, expected_shanten, description in complex_cases:
            with self.subTest(hand=hand_str, desc=description):
                tiles34, _ = str_to_tiles34(hand_str)
                result = analyze_13(tiles34)
                
                self.assertEqual(expected_shanten, result.shanten,
                               f"Shanten mismatch for {description}")
                
                # 检查综合评分是否合理
                self.assertGreaterEqual(result.mixed_waits_score, 0,
                                      "Mixed waits score should be non-negative")
    
    def test_edge_cases(self):
        """测试边界情况"""
        edge_cases = [
            # 测试特殊情况（跳过无效的测试用例）
            ("19m 19p 19s 1234567z", 6, "国士无双"),  # 13张
        ]

        for hand_str, expected_shanten, description in edge_cases:
            with self.subTest(hand=hand_str, desc=description):
                try:
                    tiles34, _ = str_to_tiles34(hand_str)
                    shanten = calculate_shanten(tiles34)
                    self.assertEqual(expected_shanten, shanten,
                                   f"Shanten mismatch for {description}")
                except ValueError:
                    # 跳过无效的牌型
                    continue
    
    def test_performance(self):
        """测试性能 - 确保分析速度合理"""
        import time

        # 测试大量分析的性能
        tiles34, _ = str_to_tiles34("123m 456m 789m 123p 11z")  # 确保14张

        start_time = time.time()
        for _ in range(100):
            analyze_14(copy.deepcopy(tiles34))
        end_time = time.time()

        # 100次分析应该在合理时间内完成（比如2秒）
        self.assertLess(end_time - start_time, 2.0,
                       "Performance test: 100 analyses should complete within 2 seconds")


if __name__ == '__main__':
    unittest.main()
