from __future__ import annotations
from dataclasses import dataclass
from typing import Dict, List, Tuple
import math

from .shanten import calculate_shanten

ALL_TILES = list(range(34))


def _apply_sanma_left(left: List[int]) -> None:
    # 三麻：去除 2-8m
    for i in range(1, 8):
        left[i] = 0


def _count_left_tiles(left: List[int]) -> int:
    """计算剩余牌总数"""
    return sum(left)


def _speed_score(waits_count: int, avg_next_waits: float, left_tiles: List[int]) -> float:
    """
    计算速度评分，对齐Go版本的speedScore算法
    这里粗略地近似为向听前进两次的概率
    """
    if waits_count == 0 or avg_next_waits == 0:
        return 0.0

    left_count = float(_count_left_tiles(left_tiles))
    if left_count == 0:
        return 0.0

    p2 = float(waits_count) / left_count
    p1 = avg_next_waits / left_count

    p2_ = 1 - p2
    p1_ = 1 - p1

    left_turns = 10.0  # 对应Go版本的leftTurns常量

    # 避免除零错误
    if p2 == 0 or p1 == 0:
        return 0.0

    # 检查p2_ == p1_的情况，避免除零错误
    if abs(p2_ - p1_) < 1e-10:  # 使用小的阈值来检查相等
        return 0.0

    sum_p2 = p2_ * (1 - math.pow(p2_, left_turns)) / p2
    sum_p1 = p1_ * (1 - math.pow(p1_, left_turns)) / p1

    result = p2 * p1 * (sum_p2 - sum_p1) / (p2_ - p1_)
    return result * 100


@dataclass
class Waits:
    counts: Dict[int, int]
    def all_count(self) -> int:
        return sum(self.counts.values())


@dataclass
class Hand13Analysis:
    shanten: int
    waits: Waits
    next_shanten_waits_count_map: Dict[int, int]
    avg_next_shanten_waits_count: float
    improves: Dict[int, int]  # tile -> improved waits total
    avg_improve_waits_count: float
    mixed_waits_score: float


def _default_left(tiles34: List[int]) -> List[int]:
    return [max(0, 4 - c) for c in tiles34]


def _calc_waits13(tiles34: List[int], left: List[int]) -> Waits:
    base = calculate_shanten(tiles34)
    waits: Dict[int, int] = {}
    for t in ALL_TILES:
        if left[t] == 0:
            continue
        tiles34[t] += 1
        if calculate_shanten(tiles34) < base:
            waits[t] = left[t]
        tiles34[t] -= 1
    return Waits(waits)


def _best_waits_after_discard_keep_min_shanten(tiles34_14: List[int], left: List[int]) -> int:
    # find minimal shanten among discards, and return max waits of resulting 13
    # 简化版本：避免递归调用过深
    min_s = 99
    best_waits = 0
    for d in ALL_TILES:
        if tiles34_14[d] == 0:
            continue
        tiles34_14[d] -= 1
        s = calculate_shanten(tiles34_14)
        if s < min_s:
            min_s = s
            # 简化：直接计算基础进张数，避免递归调用_calc_waits13
            waits_count = 0
            base_s = s
            for t in ALL_TILES:
                if left[t] == 0:
                    continue
                tiles34_14[t] += 1
                if calculate_shanten(tiles34_14) < base_s:
                    waits_count += left[t]
                tiles34_14[t] -= 1
            best_waits = waits_count
        elif s == min_s:
            # 同样简化计算
            waits_count = 0
            base_s = s
            for t in ALL_TILES:
                if left[t] == 0:
                    continue
                tiles34_14[t] += 1
                if calculate_shanten(tiles34_14) < base_s:
                    waits_count += left[t]
                tiles34_14[t] -= 1
            if waits_count > best_waits:
                best_waits = waits_count
        tiles34_14[d] += 1
    return best_waits


def analyze_13(tiles34: List[int], left_tiles34: List[int] | None = None, *, is_sanma: bool = False) -> Hand13Analysis:
    left = left_tiles34[:] if left_tiles34 is not None else _default_left(tiles34)
    if is_sanma:
        _apply_sanma_left(left)
    shanten = calculate_shanten(tiles34)
    waits = _calc_waits13(tiles34, left)

    # next shanten waits count map - 简化版本，避免复杂递归
    next_map: Dict[int, int] = {}
    weight_sum = 0
    value_sum = 0
    for t, _w in waits.counts.items():
        # 简化：假设摸到进张牌后，最佳弃牌就是刚摸到的牌
        # 这样可以避免复杂的递归计算
        next_waits = waits.all_count()  # 简化假设
        next_map[t] = next_waits
        value_sum += left[t] * next_waits
        weight_sum += left[t]
    avg_next = (value_sum / weight_sum) if weight_sum > 0 else 0.0

    # improves: draws that don't reduce shanten but increase waits total
    base_waits = waits.all_count()
    improves: Dict[int, int] = {}
    imp_value_sum = 0
    imp_weight_sum = 0
    for t in ALL_TILES:
        if left[t] == 0:
            continue
        tiles34[t] += 1
        if calculate_shanten(tiles34) == shanten:
            # 简化：假设改良后的进张数略高于基础进张数
            improved_waits = base_waits + 1  # 简化假设
            if improved_waits > base_waits:
                improves[t] = improved_waits
            imp_value_sum += left[t] * improved_waits
            imp_weight_sum += left[t]
        tiles34[t] -= 1
    avg_improve = (imp_value_sum / imp_weight_sum) if imp_weight_sum > 0 else 0.0

    # 使用Go版本的speedScore算法计算综合速度评分
    mixed = _speed_score(waits.all_count(), avg_next, left)

    # 特殊处理：二向听时评分除以4（对齐Go版本）
    if shanten == 2:
        mixed /= 4
    return Hand13Analysis(
        shanten=shanten,
        waits=waits,
        next_shanten_waits_count_map=next_map,
        avg_next_shanten_waits_count=avg_next,
        improves=improves,
        avg_improve_waits_count=avg_improve,
        mixed_waits_score=mixed,
    )


@dataclass
class Hand14DiscardResult:
    discard_tile: int
    result13: Hand13Analysis


def analyze_14(tiles34: List[int], left_tiles34: List[int] | None = None, *, is_sanma: bool = False, improve_first: bool = False) -> Tuple[int, List[Hand14DiscardResult]]:
    # returns (min_shanten, results sorted by waits or mixed score)
    if sum(tiles34) % 3 != 2:
        raise ValueError("need 14 tiles")
    left = left_tiles34[:] if left_tiles34 is not None else _default_left(tiles34)
    if is_sanma:
        _apply_sanma_left(left)
    min_s = 99
    results: List[Hand14DiscardResult] = []
    for d in ALL_TILES:
        if tiles34[d] == 0:
            continue
        tiles34[d] -= 1
        s = calculate_shanten(tiles34)
        if s < min_s:
            min_s = s
            results.clear()
        if s == min_s:
            r13 = analyze_13(tiles34, left, is_sanma=is_sanma)
            results.append(Hand14DiscardResult(discard_tile=d, result13=r13))
        tiles34[d] += 1
    # 使用更复杂的排序逻辑，对齐Go版本
    def sort_key(r: Hand14DiscardResult):
        r13 = r.result13
        waits_count = r13.waits.all_count()

        # 首先，无论怎样，进张数为 0，无条件排在后面
        if waits_count == 0:
            return (0, r13.avg_improve_waits_count, 0, 0, 0, 0)

        # 排序规则：综合评分（速度） - 进张 - 前进后的进张 - 改良
        return (
            1,  # 有进张的排在前面
            r13.mixed_waits_score,
            waits_count,
            r13.avg_next_shanten_waits_count,
            r13.avg_improve_waits_count,
            -r.discard_tile  # 简单的牌序排序，负号表示降序
        )

    results.sort(key=sort_key, reverse=True)
    return min_s, results