from mahjong_py import str_to_tiles34, calculate_shanten
from mahjong_py.shanten_improves import analyze_14, _default_left, ALL_TILES
import copy

# 测试analyze_14函数
print("Testing analyze_14...")

# 测试用例：14张牌
tiles34, _ = str_to_tiles34("123m456m789m 123p 11z")
print(f"tiles34: {tiles34}")
print(f"sum: {sum(tiles34)}")

left = _default_left(tiles34)
print(f"left tiles initialized")

# 手动测试每个可能的切牌
print("Manual discard testing:")
min_s = 99
for d in ALL_TILES:
    if tiles34[d] == 0:
        continue
    
    print(f"Testing discard {d}...")
    tiles34[d] -= 1
    
    try:
        s = calculate_shanten(tiles34)
        print(f"After discarding {d}, shanten: {s}")
        if s < min_s:
            min_s = s
    except Exception as e:
        print(f"Error calculating shanten after discarding {d}: {e}")
        tiles34[d] += 1
        break
    
    tiles34[d] += 1

print(f"Manual min shanten: {min_s}")

# 现在测试analyze_14
print("Now testing analyze_14...")
try:
    ms, rlist = analyze_14(copy.deepcopy(tiles34))
    print(f"analyze_14 min shanten: {ms}")
    print(f"Number of results: {len(rlist)}")
    if rlist:
        print(f"Top result: discard {rlist[0].discard_tile}, waits: {rlist[0].result13.waits.all_count()}")
except Exception as e:
    print(f"Error in analyze_14: {e}")
    import traceback
    traceback.print_exc()
