from __future__ import annotations
from typing import List
from .divide import DivideResult
from .yaku_data import *
from .config import get_consider_old_yaku


class HandInfo:
    def __init__(self, tiles34: List[int], divide: DivideResult, melds: List[tuple], win_tile: int,
                 is_tsumo: bool, is_riichi: bool, is_daburii: bool,
                 round_wind: int = 27, self_wind: int = 27):
        self.tiles34 = tiles34
        self.divide = divide
        self.melds = melds
        self.win_tile = win_tile
        self.is_tsumo = is_tsumo
        self.is_riichi = is_riichi
        self.is_daburii = is_daburii
        self.round_wind = round_wind
        self.self_wind = self_wind
        self._all_shuntsu_first = None
        self._all_kotsu_tiles = None

    def is_naki(self) -> bool:
        return len(self.melds) > 0

    def all_shuntsu_first(self) -> List[int]:
        if self._all_shuntsu_first is None:
            lst = list(self.divide.shuntsu_first_tiles)
            for mt, idx in self.melds:
                if mt == 'chi':
                    lst.append(idx)
            self._all_shuntsu_first = lst
        return self._all_shuntsu_first

    def all_kotsu(self) -> List[int]:
        if self._all_kotsu_tiles is None:
            lst = list(self.divide.kotsu_tiles)
            for mt, idx in self.melds:
                if mt in ('pon', 'kan'):
                    lst.append(idx)
            self._all_kotsu_tiles = lst
        return self._all_kotsu_tiles

    def contain_honor(self) -> bool:
        if self.divide.pair_tile >= 27:
            return True
        for s in self.all_shuntsu_first():
            if s >= 27 or s + 2 >= 27:
                return True
        for k in self.all_kotsu():
            if k >= 27:
                return True
        return False

    def is_yaku_tile(self, tile: int) -> bool:
        if tile >= 31:
            return True  # 三元
        return tile == self.round_wind or tile == self.self_wind


def _is_yaochu(tile: int) -> bool:
    return tile >= 27 or tile % 9 in (0, 8)


def _is_man(tile: int) -> bool:
    return 0 <= tile < 9


def _is_pin(tile: int) -> bool:
    return 9 <= tile < 18


def _is_sou(tile: int) -> bool:
    return 18 <= tile < 27


def yaku_normal(hi: HandInfo) -> List[int]:
    yaku: List[int] = []
    is_naki = hi.is_naki()
    # 立直、自摸、双立直
    if not is_naki and hi.is_daburii:
        yaku.append(YakuDaburii)
    if not is_naki and hi.is_riichi and not hi.is_daburii:
        yaku.append(YakuRiichi)
    if not is_naki and hi.is_tsumo:
        yaku.append(YakuTsumo)
    # 七对
    if hi.divide.is_chiitoi and not is_naki:
        yaku.append(YakuChiitoi)
    # 平和
    if not is_naki:
        if len(hi.divide.shuntsu_first_tiles) == 4 and not hi.is_yaku_tile(hi.divide.pair_tile):
            two_sided = False
            for s in hi.divide.shuntsu_first_tiles:
                t9 = s % 9
                if (t9 < 6 and s == hi.win_tile) or (t9 > 0 and s + 2 == hi.win_tile):
                    two_sided = True
                    break
            if two_sided:
                yaku.append(YakuPinfu)
    # 一杯口/两杯口
    if not is_naki:
        if hi.divide.is_ryanpeikou:
            yaku.append(YakuRyanpeikou)
        elif hi.divide.is_iipeikou:
            yaku.append(YakuIipeikou)
    # 三色同顺
    s_all = hi.all_shuntsu_first()
    if len(s_all) >= 3:
        s_man = [s for s in s_all if _is_man(s)]
        s_pin = [s for s in s_all if _is_pin(s)]
        s_sou = [s for s in s_all if _is_sou(s)]
        for m in s_man:
            if m + 9 in s_pin and m + 18 in s_sou:
                yaku.append(YakuSanshokuDoujun)
                break
    # 一气
    has_chi = any(mt == 'chi' for mt, _ in hi.melds)
    if not has_chi:
        if hi.divide.is_ittsuu:
            yaku.append(YakuIttsuu)
    else:
        st = s_all
        for t in st:
            if t % 9 == 0 and (t + 3) in st and (t + 6) in st:
                yaku.append(YakuIttsuu)
                break
    # 对对
    if len(hi.all_kotsu()) == 4:
        yaku.append(YakuToitoi)
    # 断幺
    if not hi.contain_honor():
        ok = True
        if _is_yaochu(hi.divide.pair_tile):
            ok = False
        for s in s_all:
            if _is_yaochu(s) or _is_yaochu(s + 2):
                ok = False
                break
        for k in hi.all_kotsu():
            if _is_yaochu(k):
                ok = False
                break
        if ok:
            yaku.append(YakuTanyao)
    # 役牌（连风=2个役牌）
    yakuhai_count = 0
    for k in hi.all_kotsu():
        if hi.is_yaku_tile(k):
            yakuhai_count += 1
            if k == hi.round_wind == hi.self_wind:
                yakuhai_count += 1
    for _ in range(yakuhai_count):
        yaku.append(YakuYakuhai)
    # 混一/清一
    def num_suit() -> int:
        cnt = [0, 0, 0]
        def add(tile: int):
            if _is_man(tile): cnt[0] += 1
            elif _is_pin(tile): cnt[1] += 1
            elif _is_sou(tile): cnt[2] += 1
        if hi.divide.is_chiitoi:
            for i, c in enumerate(hi.tiles34[:27]):
                if c > 0: add(i)
        else:
            add(hi.divide.pair_tile)
            for s in s_all: add(s)
            for k in hi.all_kotsu(): add(k)
        return sum(1 for x in cnt if x > 0)
    ns = num_suit()
    if hi.contain_honor():
        if ns == 1:
            yaku.append(YakuHonitsu)
    else:
        if ns == 1:
            yaku.append(YakuChinitsu)

    # 老役
    if get_consider_old_yaku():
        from .yaku_old import find_old_yaku
        old_yaku = find_old_yaku(hi, is_naki)
        yaku.extend(old_yaku)

    return yaku